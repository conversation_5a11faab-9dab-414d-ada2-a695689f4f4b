@echo off
echo ITrace统计信息提取器
echo =====================

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

REM 检查脚本文件是否存在
if not exist "itrace_statistics_extractor.py" (
    echo 错误: 未找到itrace_statistics_extractor.py文件
    pause
    exit /b 1
)

echo.
echo 使用方法:
echo 1. 扫描当前目录
echo 2. 指定扫描路径
echo 3. 查看帮助信息
echo.

set /p choice="请选择操作 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 正在扫描当前目录...
    python itrace_statistics_extractor.py
) else if "%choice%"=="2" (
    set /p scan_path="请输入要扫描的路径: "
    echo.
    echo 正在扫描路径: %scan_path%
    python itrace_statistics_extractor.py --path "%scan_path%"
) else if "%choice%"=="3" (
    python itrace_statistics_extractor.py --help
) else (
    echo 无效选择
)

echo.
pause

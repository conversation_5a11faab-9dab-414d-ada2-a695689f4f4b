InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125
7,0x000000001028,0x00000000fb567890,"tmma.tnt t4, t2, t3",1130,1135,1140,1145,1165
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195
9,0x000000001038,0x00000000fb789012,"tld.global.u64 t8, (x13)",1200,1205,1210,1215,1250
10,0x000000001040,0x00000000fb890123,"tmma.ttn t9, t5, t6",1255,1260,1265,1270,1290
11,0x000000001048,0x00000000fb901234,"tcsrr.i t10",1295,1300,1305,1310,1315
12,0x000000001050,0x00000000fb012345,"tst.shared.u32 (x14), t11",1320,1325,1330,1335,1380
13,0x000000001058,0x00000000fb123456,"tld.texture.u32 t12, (x15)",1385,1390,1395,1400,1420
14,0x000000001060,0x00000000fb234567,"tmma.ntt t13, t7, t8",1425,1430,1435,1440,1465
15,0x000000001068,0x00000000fb345678,"tcsrw.i 0x10",1470,1475,1480,1485,1490
16,0x000000001070,0x00000000fb456789,"tld.linear.u8 t14, (x16)",1495,1500,1505,1510,1530
17,0x000000001078,0x00000000fb567890,"tmma.ntn t15, t9, t10",1535,1540,1545,1550,1580
18,0x000000001080,0x00000000fb678901,"tst.global.u64 (x17), t16",1585,1590,1595,1600,1620
19,0x000000001088,0x00000000fb789012,"tld.shared.u16 t17, (x18)",1625,1630,1635,1640,1655
20,0x000000001090,0x00000000fb890123,"tmma.nnn t18, t11, t12",1660,1665,1670,1675,1720

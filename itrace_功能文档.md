# itrace.sv 功能实现详细文档

## 概述

`itrace.sv` 是一个用于 NPU (Neural Processing Unit) 性能监控的 SystemVerilog 模块。该模块主要用于跟踪和分析指令流水线的性能，包括取指、译码、分发、执行等各个阶段的时延，以及功能单元利用率和指令断流统计。

## 主要模块结构

### 模块声明
```systemverilog
module itrace_monitor (
    input logic clk,
    input logic pe_resetn
);
```

## 核心功能组件

### 1. 指令跟踪结构体 (`instr_perf_t`)

```systemverilog
typedef struct {
    logic [47:0] pc;          // 扩展到48位的程序计数器
    logic [127:0] instr;      // 128位宽，存储完整的tile指令
    logic [50:0] tinstr;      // 51位宽，存储dispatch阶段的tile指令
    string disasm_str;        // 反编译指令字符串
    logic [31:0] itag;        // 指令标签
    realtime fetch_start, fetch_end;       // 取指阶段时间
    realtime decode_start, decode_end;     // 译码阶段时间  
    realtime dispatch_start, dispatch_end; // 分发阶段时间
    realtime execute_start, execute_end;   // 执行阶段时间
    bit valid;                // 有效标志
} instr_perf_t;
```

### 2. 取指请求跟踪结构体 (`fetch_req_t`)

```systemverilog
typedef struct {
    logic [47:0] req_addr_48bit; // 48位完整取指地址（用于取指请求时的比较）
    logic [31:0] req_addr_32bit; // 32位取指地址（用于其他时候的比较）
    realtime fetch_time;         // 取指开始时间
} fetch_req_t;
```

## 核心功能实现

### 1. 取指请求管理

#### 1.1 添加取指请求
- **函数**: `add_fetch_request()`
- **功能**: 管理取指请求队列，支持地址覆盖机制
- **特点**: 
  - 使用48位地址比较确保精确匹配
  - 相同地址的请求会覆盖之前的请求

#### 1.2 处理64位取指请求
- **函数**: `process_64bit_fetch_request()`
- **功能**: 将64位取指请求拆分为两个32位请求
- **实现**: 生成地址 `addr` 和 `addr+4` 的两个取指请求

#### 1.3 查找和删除取指时间
- **函数**: `find_and_remove_fetch_time()`
- **功能**: 根据PC地址查找对应的取指时间并删除记录
- **比较策略**: 使用低32位地址进行比较

### 2. 指令收集器（Tile指令处理）

#### 2.1 指令收集逻辑
- **支持类型**: 32位和多字tile指令
- **双通道处理**: 同时监控CH0和CH1两个指令通道
- **收集策略**:
  - 单通道有tile指令时独立收集
  - 双通道同时有tile指令时优先处理CH0，CH1作为第二个字
  - 多字指令需要跨周期收集完整

#### 2.2 特殊指令处理
- **Sync/Wait指令**: 在fetch后直接丢弃，不进入后续pipeline
- **处理方式**: 记录fetch和decode start时间后直接写入日志

### 3. 指令流水线监控

#### 3.1 各阶段时间跟踪
1. **Fetch阶段**: 
   - 开始时间：取指请求发出时间
   - 结束时间：指令返回时间

2. **Decode阶段**:
   - 开始时间：取指完成时间
   - 结束时间：dispatch开始时间

3. **Dispatch阶段**:
   - 开始时间：指令进入dispatch队列时间
   - 结束时间：指令开始执行时间

4. **Execute阶段**:
   - 开始时间：指令开始执行时间
   - 结束时间：指令执行完成时间

#### 3.2 执行单元监控
监控的功能单元包括：
- **TMAC**: Tensor MAC单元
- **TSFU**: Tensor Special Function Unit
- **TALU**: Tensor ALU单元  
- **TLD**: Tensor Load单元
- **TST**: Tensor Store单元

### 4. 性能统计功能

#### 4.1 指令断流统计
- **触发条件**: 检测到0x9d02指令（c.jalr s10）开始监控
- **结束条件**: 检测到thread_done信号
- **统计内容**:
  - 总监控时间
  - 断流时间和百分比
  - Tile指令总数

#### 4.2 功能单元利用率统计
- **统计周期**: 在监控期间的每个时钟周期
- **统计指标**: 各功能单元的活跃周期数和利用率百分比

### 5. 调试支持

#### 5.1 调试开关
- **控制方式**: 通过`$test$plusargs("ITRACE_DEBUG_ENABLE")`动态控制
- **调试文件**: `itrace_debug.log`
- **调试内容**: 详细的信号状态、指令流转过程

#### 5.2 错误分析
- **未完成指令分析**: 生成`incomplete_instructions.log`
- **未匹配取指请求**: 生成`unmatched_fetch_requests.log`
- **详细分析**: 包含各阶段停留时间和卡死原因

## 输出文件格式

### 1. 主要性能日志 (`itrace.log`)
```csv
InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
```

### 2. 统计信息
- 指令断流统计
- 功能单元利用率统计
- 总体性能指标

### 3. 错误分析日志
- 未完成指令详细分析
- 未匹配取指请求分析

## 关键技术特点

### 1. 双通道指令处理
- 同时监控两个指令通道（CH0和CH1）
- 智能处理并发指令和多字指令

### 2. 精确时间跟踪
- 使用realtime类型确保时间精度
- 跟踪指令在各流水线阶段的详细时延

### 3. 灵活的地址处理
- 支持48位扩展地址和32位兼容地址
- 智能地址匹配机制

### 4. 全面的性能分析
- 指令级性能分析
- 系统级利用率统计
- 断流分析和瓶颈识别

### 5. 健壮的错误处理
- 完善的未完成指令分析
- 详细的错误日志和诊断信息

## 应用场景

1. **性能调试**: 识别指令流水线瓶颈
2. **功能单元分析**: 评估各执行单元利用率
3. **系统优化**: 基于断流统计优化调度策略
4. **验证测试**: 确保指令流水线正确性

## 总结

`itrace.sv`是一个功能完善的NPU性能监控模块，提供了从指令取指到执行完成的全流程跟踪能力，支持复杂的多字指令处理和双通道并发监控，能够生成详细的性能分析报告，是NPU系统性能分析和调试的重要工具。

# ITrace Timeline Visualizer 错误处理增强

## 概述

本次更新为 ITrace Timeline Visualizer 增加了详细的错误处理功能，使程序在面对数据格式错误时能够：
1. 提供具体的错误信息，包括出错的行号和错误原因
2. 跳过错误数据，继续处理有效数据
3. 支持注释行（以"//"开头的行）
4. 即使在有错误的情况下也能生成HTML文件

## 主要改进

### 1. 注释行支持
- 以"//"开头的行被识别为注释行
- 注释行会被自动跳过，不会产生错误信息
- 程序会显示跳过的注释行数量

### 2. 详细的错误报告
程序现在会报告以下类型的错误，并指出具体的行号：

#### 文件级别错误
- 文件不存在
- 文件为空
- 文件编码问题
- 权限错误
- 内存不足

#### 数据格式错误
- CSV解析错误
- 缺少必要的列
- 无效的InstrID（非数字）
- 无效的时间数据（非数字）
- 数据类型转换失败

### 3. 容错处理
- **缺少列**: 自动为缺少的列添加默认值
- **无效数据**: 跳过无效行，继续处理有效数据
- **时间数据错误**: 安全地转换时间数据，无法转换的使用默认值
- **空数据**: 即使没有有效数据也会生成空的HTML文件

### 4. 改进的用户反馈
- 使用表情符号和颜色标记不同类型的消息
- 详细的进度报告
- 错误统计和摘要
- 文件大小和路径信息

## 使用示例

### 测试文件格式
```csv
// 这是注释行 - 会被忽略
// 另一个注释说明数据格式
InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime

// 有效数据行
1,0x1000,0xfb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020

// 这行有无效的InstrID
invalid_id,0x1010,0xfb0012ab,"twait",1050,1055,0,0,0

// 这行有无效的时间数据
4,0x1014,0xfb801000,"ace_bsync x0",invalid_time,1065,0,0,0

// 有效数据继续
5,0x1018,0xfb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
```

### 运行结果示例
```
============================================================
ITrace Timeline Visualizer
============================================================
输入文件: test_with_comments_and_errors.log
输出文件: test_output.html
生成时间: 2025-08-07 15:12:11
------------------------------------------------------------
开始创建可视化...
正在读取文件: test_with_comments_and_errors.log (大小: 1210 字节)
跳过了 15 行注释
文件头部: InstrID,PC,Instruction,Disassembly,FetchStartTime...
成功读取CSV文件，共 8 行数据
正在验证数据格式...
⚠️  发现 2 行数据格式错误:
  第4行: InstrID 'invalid_id' 不是有效数字
  第5行: FetchStartTime 'invalid_time' 不是有效数字
有效数据行数: 6
将跳过错误行，继续处理有效数据...
正在过滤有效数据...
正在计算时间范围...
✅ 成功加载 8 条指令记录
📊 时间范围: 1000.0 - 1195.0
正在处理 8 条指令数据...
⚠️  处理过程中发现 2 个错误:
  第4行: InstrID 'invalid_id' 无法转换为整数
  第5行: fetch阶段时间转换失败
已跳过错误数据，继续处理...
✅ 成功处理 7 条指令数据
📝 正在生成HTML可视化文件: test_output.html
✅ HTML可视化文件已生成: test_output.html
📊 包含 7 条指令的时间线
⏱️  时间范围: 1000.0 - 1195.0
📁 文件大小: 0.05 MB
📍 文件路径: D:\proj\sv_dump\test_output.html
------------------------------------------------------------
✅ 可视化生成成功!
```

## 错误类型说明

### 警告级别错误（⚠️）
这些错误不会阻止程序运行，但会跳过有问题的数据：
- 数据格式错误
- 无效的数值
- 缺少的列
- 时间数据转换失败

### 致命错误（❌）
这些错误会导致程序无法继续：
- 文件不存在
- 权限错误
- 内存不足
- 严重的系统错误

## 技术实现

### 预处理阶段
1. 读取原始文件
2. 过滤注释行和空行
3. 生成临时CSV文件
4. 清理临时文件

### 数据验证阶段
1. 检查必要的列
2. 验证数据类型
3. 统计错误信息
4. 添加默认值

### 容错处理
1. 安全的数值转换（使用pd.to_numeric）
2. 异常捕获和处理
3. 继续处理有效数据
4. 生成详细的错误报告

## 兼容性

- 支持pandas 1.x和2.x版本
- 兼容不同的CSV格式
- 支持UTF-8编码
- 跨平台兼容（Windows/Linux/macOS）

## 总结

通过这些改进，ITrace Timeline Visualizer现在能够：
- 处理包含错误的真实数据文件
- 提供详细的错误诊断信息
- 支持注释和文档化的数据文件
- 在任何情况下都尽可能生成有用的输出
- 为用户提供清晰的反馈和指导

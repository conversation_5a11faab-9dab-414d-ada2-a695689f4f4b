InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125
7,0x000000001028,0x00000000fb567890,"tmma.tnt t4, t2, t3",1130,1135,1140,1145,1165
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195

// ===== INSTRUCTION STALL STATISTICS =====
// Stall monitor start time (0x9d02 fetch): 1000.00
// Stall monitor end time (thread_done): 5000.00
// Total monitor time: 4000.00
// Total stall time: 800.00
// Stall percentage: 20.00%
// Total tile instructions: 10

// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====
// Total monitor cycles: 4000
// TMAC utilization: 75.50% (3020/4000 cycles)
// TSFU utilization: 45.25% (1810/4000 cycles)
// TALU utilization: 60.75% (2430/4000 cycles)
// TLD utilization: 55.00% (2200/4000 cycles)
// TST utilization: 30.25% (1210/4000 cycles)

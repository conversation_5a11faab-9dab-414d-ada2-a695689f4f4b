
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }

        .timeline-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            width: 100%;
            max-width: 100%;
            display: flex;
            flex-direction: column;
        }

        .timeline-content {
            display: flex;
            position: relative;
            width: 100%;
        }

        .fixed-left-panel {
            width: 350px;
            flex-shrink: 0;
            background: #fafafa;
            border-right: 2px solid #e0e0e0;
            z-index: 20;
            position: relative;
        }

        .scrollable-right-panel {
            flex: 1;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
        }

        #instructionList {
            position: relative;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-left: auto;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .time-axis {
            position: relative;
            height: 40px;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            min-width: 800px;
            overflow: visible;
        }

        .time-tick {
            position: absolute;
            bottom: 0;
            width: 1px;
            height: 10px;
            background: #666;
        }

        .time-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: translateX(-50%);
        }

        .instruction-row {
            display: flex;
            align-items: stretch;
            margin-bottom: 8px;
            min-height: 45px;
            position: relative;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .instruction-left {
            width: 350px;
            padding: 8px;
            background: #fafafa;
            border-radius: 8px 0 0 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 45px;
            box-sizing: border-box;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .instruction-left:hover {
            background: #e3f2fd;
        }

        .instruction-left:active {
            background: #bbdefb;
        }

        .instruction-left.highlighted,
        .instruction-right.highlighted {
            background: #fff3cd !important;
            border: 2px solid #ffc107 !important;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5) !important;
            animation: highlight-pulse 0.5s ease-in-out;
        }

        @keyframes highlight-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .instruction-right {
            flex: 1;
            background: #fafafa;
            border-radius: 0 8px 8px 0;
            position: relative;
            min-width: 800px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        .instruction-row:hover {
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .instruction-info {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .instruction-pc {
            color: #666;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .instruction-disasm {
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            font-size: 1.0em;
            font-weight: bold;
            word-break: break-all;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }

        .timeline-bars {
            position: relative;
            width: 100%;
            margin: 8px;
            overflow: visible;
            flex: 1;
            min-height: 29px;
        }

        .stage-bar {
            position: absolute;
            height: 20px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.7);
            cursor: pointer;
            transition: all 0.2s ease;
            top: 50%;
            transform: translateY(-50%);
            border: 1px solid rgba(0,0,0,0.2);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .stage-bar:hover {
            transform: translateY(-50%) scale(1.05);
            z-index: 100 !important;
            box-shadow: 0 3px 6px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.8);
        }

        /* 所有阶段都在同一高度 - 垂直居中 */
        .stage-bar.fetch {
            z-index: 4;
        }
        .stage-bar.decode {
            z-index: 3;
        }
        .stage-bar.dispatch {
            z-index: 2;
        }
        .stage-bar.execute {
            z-index: 1;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            color: #555;
        }

        .control-group input, .control-group select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .summary {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .summary h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .summary-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .summary-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>12</strong></div>
            <div>时间范围: <strong>1000 - 1315</strong></div>
            <div>总执行时间: <strong>315</strong></div>
            <div>生成时间: <strong>2025-08-07 16:12:54</strong></div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline-header">
            <h3 style="margin: 0;">指令执行时间线</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>Fetch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ECDC4;"></div>
                    <span>Decode</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>Dispatch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96CEB4;"></div>
                    <span>Execute</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>缩放:</label>
                <input type="range" id="zoomSlider" min="0.1" max="20" step="0.1" value="1">
                <span id="zoomValue">1.0x</span>
            </div>
            <div class="control-group">
                <label>过滤指令:</label>
                <input type="text" id="filterInput" placeholder="输入PC地址或反汇编代码...">
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortSelect">
                    <option value="id">指令ID</option>
                    <option value="start_time">开始时间</option>
                    <option value="duration">执行时长</option>
                </select>
            </div>
            <button class="btn" onclick="resetView()">重置视图</button>
            <div class="control-group">
                <label>操作说明:</label>
                <span style="font-size: 12px; color: #666;">W/S: 放大/缩小 | A/D: 左移/右移 | R: 复位缩放 | 鼠标拖拽: 上拖放大/下拖缩小 | 点击指令名: 居中显示</span>
            </div>
        </div>

        <div class="timeline-content">
            <div class="fixed-left-panel">
                <div style="height: 40px; margin-bottom: 20px;"></div>
                <div id="leftPanelContent"></div>
            </div>
            <div class="scrollable-right-panel">
                <div class="time-axis" id="timeAxis"></div>
                <div id="instructionList"></div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <div class="summary">
        <h3>执行统计摘要</h3>
        <div class="summary-grid">
            
            <div class="summary-item">
                <div class="summary-value">12</div>
                <div class="summary-label">总指令数</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">22.5</div>
                <div class="summary-label">平均执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">35</div>
                <div class="summary-label">最长执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5</div>
                <div class="summary-label">最短执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Fetch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Decode时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Dispatch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">11.0</div>
                <div class="summary-label">平均Execute时间</div>
            </div>
        
        </div>
    </div>

    <script>
        const instructions = [{"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1025, "end": 1030, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1030, "end": 1035, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1040, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1055, "duration": 15, "color": "#96CEB4"}], "total_start": 1025, "total_end": 1055, "total_duration": 30}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb0012ab", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1050, "end": 1055, "duration": 5, "color": "#FF6B6B"}], "total_start": 1050, "total_end": 1055, "total_duration": 5}, {"id": 4, "pc": "0x000000001014", "instruction": "0x00000000fb801000", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1060, "end": 1065, "duration": 5, "color": "#FF6B6B"}], "total_start": 1060, "total_end": 1065, "total_duration": 5}, {"id": 5, "pc": "0x000000001018", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1070, "end": 1075, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1075, "end": 1080, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1080, "end": 1085, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1090, "duration": 5, "color": "#96CEB4"}], "total_start": 1070, "total_end": 1090, "total_duration": 20}, {"id": 6, "pc": "0x000000001020", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1095, "end": 1100, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1100, "end": 1105, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1105, "end": 1110, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1110, "end": 1125, "duration": 15, "color": "#96CEB4"}], "total_start": 1095, "total_end": 1125, "total_duration": 30}, {"id": 7, "pc": "0x000000001028", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1130, "end": 1135, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1135, "end": 1140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1140, "end": 1145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1145, "end": 1165, "duration": 20, "color": "#96CEB4"}], "total_start": 1130, "total_end": 1165, "total_duration": 35}, {"id": 8, "pc": "0x000000001030", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1170, "end": 1175, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1175, "end": 1180, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1180, "end": 1185, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1185, "end": 1195, "duration": 10, "color": "#96CEB4"}], "total_start": 1170, "total_end": 1195, "total_duration": 25}, {"id": 9, "pc": "0x000000001038", "instruction": "0x00000000fb789012", "disassembly": "very_long_instruction_name_that_should_cause_wrapping_in_the_display_area_to_test_dynamic_height_adjustment t8, t9, t10", "stages": [{"name": "fetch", "start": 1200, "end": 1205, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1205, "end": 1210, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1210, "end": 1215, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1215, "end": 1230, "duration": 15, "color": "#96CEB4"}], "total_start": 1200, "total_end": 1230, "total_duration": 30}, {"id": 10, "pc": "0x000000001040", "instruction": "0x00000000fb890123", "disassembly": "short", "stages": [{"name": "fetch", "start": 1235, "end": 1240, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1240, "end": 1245, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1245, "end": 1250, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1250, "end": 1255, "duration": 5, "color": "#96CEB4"}], "total_start": 1235, "total_end": 1255, "total_duration": 20}, {"id": 11, "pc": "0x000000001048", "instruction": "0x00000000fb901234", "disassembly": "another_extremely_long_instruction_name_with_many_parameters_and_complex_addressing_modes_that_will_definitely_wrap_to_multiple_lines t11, (x13+offset), immediate_value", "stages": [{"name": "fetch", "start": 1260, "end": 1265, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1265, "end": 1270, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1270, "end": 1275, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1275, "end": 1290, "duration": 15, "color": "#96CEB4"}], "total_start": 1260, "total_end": 1290, "total_duration": 30}, {"id": 12, "pc": "0x000000001050", "instruction": "0x00000000fb012345", "disassembly": "add x1, x2", "stages": [{"name": "fetch", "start": 1295, "end": 1300, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1300, "end": 1305, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1305, "end": 1310, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1310, "end": 1315, "duration": 5, "color": "#96CEB4"}], "total_start": 1295, "total_end": 1315, "total_duration": 20}];
        const minTime = 1000;
        const maxTime = 1315;
        const timeRange = maxTime - minTime;

        let currentZoom = 1.0;
        let currentFilter = '';
        let currentSort = 'id';
        let currentScrollLeft = 0;

        // 拖拽缩放相关变量
        let isDragging = false;
        let dragStartY = 0;
        let dragStartZoom = 1.0;

        // 初始化时间轴
        function initTimeAxis() {
            const timeAxis = document.getElementById('timeAxis');
            timeAxis.innerHTML = ''; // 清空现有内容

            // 计算时间轴的实际宽度（像素）
            const baseWidth = 800;
            const actualWidth = baseWidth * currentZoom;

            // 根据缩放级别调整刻度数量，确保刻度密度合适
            const baseTickCount = 10;
            const tickCount = Math.max(baseTickCount, Math.floor(baseTickCount * currentZoom));

            for (let i = 0; i <= tickCount; i++) {
                const time = minTime + (timeRange * i / tickCount);
                // 使用像素位置而不是百分比
                const positionPx = (i / tickCount) * actualWidth;

                const tick = document.createElement('div');
                tick.className = 'time-tick';
                tick.style.left = positionPx + 'px';
                timeAxis.appendChild(tick);

                const label = document.createElement('div');
                label.className = 'time-label';
                label.style.left = positionPx + 'px';
                label.textContent = Math.round(time);
                timeAxis.appendChild(label);
            }
        }

        // 计算时间条的位置和宽度
        function calculateBarPosition(start, end) {
            const baseWidth = 800;
            const actualWidth = baseWidth * currentZoom;

            // 计算像素位置
            const startPx = ((start - minTime) / timeRange) * actualWidth;
            const widthPx = ((end - start) / timeRange) * actualWidth;

            return {
                left: startPx + 'px',
                width: Math.max(widthPx, 2) + 'px'  // 最小宽度2px
            };
        }

        // 显示工具提示
        function showTooltip(event, stage, instruction) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${stage.name.toUpperCase()}</strong><br>
                指令: ${instruction.disassembly}<br>
                开始: ${stage.start}<br>
                结束: ${stage.end}<br>
                持续: ${stage.duration}
            `;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.style.opacity = '1';
        }

        // 隐藏工具提示
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }

        // 渲染指令列表
        function renderInstructions(filteredInstructions = null) {
            const instructionList = document.getElementById('instructionList');
            const leftPanelContent = document.getElementById('leftPanelContent');
            const instructionsToRender = filteredInstructions || instructions;

            instructionList.innerHTML = '';
            leftPanelContent.innerHTML = '';

            instructionsToRender.forEach((instruction, index) => {
                // 创建左侧固定信息
                const leftInfo = document.createElement('div');
                leftInfo.className = 'instruction-left';
                leftInfo.innerHTML = `
                    <div class="instruction-pc">${instruction.pc}</div>
                    <div class="instruction-disasm">${instruction.disassembly}</div>
                `;

                // 添加点击事件
                leftInfo.addEventListener('click', () => {
                    handleInstructionClick(instruction);
                });

                // 添加标题提示
                leftInfo.title = `点击居中显示指令 ID: ${instruction.id}`;

                leftPanelContent.appendChild(leftInfo);

                // 创建右侧时间线
                const rightPanel = document.createElement('div');
                rightPanel.className = 'instruction-right';

                const timeline = document.createElement('div');
                timeline.className = 'timeline-bars';
                timeline.style.width = (800 * currentZoom) + 'px';

                instruction.stages.forEach(stage => {
                    const bar = document.createElement('div');
                    bar.className = `stage-bar ${stage.name}`;
                    bar.style.backgroundColor = stage.color;

                    const position = calculateBarPosition(stage.start, stage.end);
                    bar.style.left = position.left;
                    bar.style.width = position.width;

                    // 检查宽度是否足够显示文本（从像素字符串中提取数值）
                    const widthPx = parseFloat(position.width);
                    if (widthPx > 20) {
                        bar.textContent = stage.name;
                    }

                    bar.addEventListener('mouseenter', (e) => showTooltip(e, stage, instruction));
                    bar.addEventListener('mouseleave', hideTooltip);

                    timeline.appendChild(bar);
                });

                rightPanel.appendChild(timeline);
                instructionList.appendChild(rightPanel);
            });

            // 调整高度匹配
            adjustHeights();
        }

        // 调整左右面板高度匹配
        function adjustHeights() {
            const leftPanels = document.querySelectorAll('.instruction-left');
            const rightPanels = document.querySelectorAll('.instruction-right');

            for (let i = 0; i < Math.min(leftPanels.length, rightPanels.length); i++) {
                const leftPanel = leftPanels[i];
                const rightPanel = rightPanels[i];

                // 重置高度以获取自然高度
                leftPanel.style.height = 'auto';
                rightPanel.style.height = 'auto';

                // 等待DOM更新
                setTimeout(() => {
                    const leftHeight = leftPanel.offsetHeight;
                    const rightHeight = rightPanel.offsetHeight;
                    const maxHeight = Math.max(leftHeight, rightHeight);

                    // 设置相同的高度
                    leftPanel.style.height = maxHeight + 'px';
                    rightPanel.style.height = maxHeight + 'px';
                }, 0);
            }
        }

        // 过滤指令
        function filterInstructions() {
            const filter = document.getElementById('filterInput').value.toLowerCase();
            if (!filter) {
                renderInstructions();
                return;
            }

            const filtered = instructions.filter(instr =>
                instr.pc.toLowerCase().includes(filter) ||
                instr.disassembly.toLowerCase().includes(filter)
            );

            renderInstructions(filtered);
        }

        // 排序指令
        function sortInstructions() {
            const sortBy = document.getElementById('sortSelect').value;
            const sorted = [...instructions];

            sorted.sort((a, b) => {
                switch(sortBy) {
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }
            });

            renderInstructions(sorted);
        }

        // 缩放功能
        function updateZoom() {
            const zoom = parseFloat(document.getElementById('zoomSlider').value);
            currentZoom = zoom;
            document.getElementById('zoomValue').textContent = zoom.toFixed(1) + 'x';

            // 应用缩放
            applyZoom();
        }

        // 重置视图
        function resetView() {
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('filterInput').value = '';
            document.getElementById('sortSelect').value = 'id';
            currentScrollLeft = 0;
            updateZoom();
            renderInstructions();
            // 重置滚动位置
            const timelineContainer = document.querySelector('.timeline-container');
            timelineContainer.scrollLeft = 0;
        }

        // 拖拽缩放功能
        function initDragZoom() {
            const scrollablePanel = document.querySelector('.scrollable-right-panel');

            // 鼠标按下事件
            scrollablePanel.addEventListener('mousedown', function(event) {
                // 只响应左键
                if (event.button === 0) {
                    isDragging = true;
                    dragStartY = event.clientY;
                    dragStartZoom = currentZoom;

                    // 改变鼠标样式
                    scrollablePanel.style.cursor = 'ns-resize';

                    // 阻止默认行为
                    event.preventDefault();
                }
            });

            // 鼠标移动事件
            document.addEventListener('mousemove', function(event) {
                if (isDragging) {
                    const deltaY = dragStartY - event.clientY; // 上拖为正，下拖为负
                    const sensitivity = 0.01; // 缩放敏感度
                    const zoomDelta = deltaY * sensitivity;

                    let newZoom = dragStartZoom + zoomDelta;
                    newZoom = Math.max(0.1, Math.min(20, newZoom)); // 限制缩放范围

                    // 更新缩放滑块和应用缩放
                    const zoomSlider = document.getElementById('zoomSlider');
                    zoomSlider.value = newZoom;
                    currentZoom = newZoom;
                    document.getElementById('zoomValue').textContent = newZoom.toFixed(1) + 'x';

                    // 应用缩放
                    applyZoom();

                    event.preventDefault();
                }
            });

            // 鼠标释放事件
            document.addEventListener('mouseup', function(event) {
                if (isDragging) {
                    isDragging = false;

                    // 恢复鼠标样式
                    scrollablePanel.style.cursor = '';
                }
            });

            // 鼠标离开窗口时停止拖拽
            document.addEventListener('mouseleave', function(event) {
                if (isDragging) {
                    isDragging = false;
                    scrollablePanel.style.cursor = '';
                }
            });
        }

        // 应用缩放（从updateZoom中提取出来的核心逻辑）
        function applyZoom() {
            // 更新时间轴的宽度
            const baseWidth = 800;
            const newWidth = (baseWidth * currentZoom) + 'px';

            const timeAxis = document.getElementById('timeAxis');
            timeAxis.style.width = newWidth;
            timeAxis.style.minWidth = newWidth;

            // 重新渲染时间轴和指令以应用缩放
            initTimeAxis();
            renderInstructions();
        }

        // 居中显示指定时间
        function centerOnTime(targetTime) {
            const scrollablePanel = document.querySelector('.scrollable-right-panel');
            if (!scrollablePanel) return;

            // 计算目标时间在时间轴上的像素位置
            const baseWidth = 800;
            const actualWidth = baseWidth * currentZoom;
            const timePosition = ((targetTime - minTime) / timeRange) * actualWidth;

            // 计算滚动位置，使目标时间居中
            const panelWidth = scrollablePanel.clientWidth;
            const scrollPosition = timePosition - (panelWidth / 2);

            // 限制滚动位置在有效范围内
            const maxScroll = scrollablePanel.scrollWidth - panelWidth;
            const finalScrollPosition = Math.max(0, Math.min(scrollPosition, maxScroll));

            // 平滑滚动到目标位置
            scrollablePanel.scrollTo({
                left: finalScrollPosition,
                behavior: 'smooth'
            });
        }

        // 点击指令名居中显示
        function handleInstructionClick(instruction) {
            // 找到指令的开始时间
            let startTime = instruction.total_start;

            // 如果没有总开始时间，使用第一个阶段的开始时间
            if (!startTime && instruction.stages && instruction.stages.length > 0) {
                startTime = Math.min(...instruction.stages.map(stage => stage.start));
            }

            if (startTime) {
                centerOnTime(startTime);

                // 可选：高亮显示对应的时间条（添加临时高亮效果）
                highlightInstruction(instruction.id);
            }
        }

        // 高亮指令（临时视觉反馈）
        function highlightInstruction(instructionId) {
            // 移除之前的高亮
            document.querySelectorAll('.instruction-left, .instruction-right').forEach(element => {
                element.classList.remove('highlighted');
            });

            // 添加高亮样式
            const instructionRows = document.querySelectorAll('.instruction-right');
            const leftPanels = document.querySelectorAll('.instruction-left');

            // 获取当前显示的指令列表（可能是过滤后的）
            const currentInstructions = getCurrentDisplayedInstructions();

            // 找到对应的指令行并高亮
            for (let i = 0; i < currentInstructions.length; i++) {
                const instruction = currentInstructions[i];
                if (instruction && instruction.id === instructionId) {
                    if (instructionRows[i]) {
                        instructionRows[i].classList.add('highlighted');
                    }
                    if (leftPanels[i]) {
                        leftPanels[i].classList.add('highlighted');
                    }

                    // 3秒后移除高亮
                    setTimeout(() => {
                        if (instructionRows[i]) {
                            instructionRows[i].classList.remove('highlighted');
                        }
                        if (leftPanels[i]) {
                            leftPanels[i].classList.remove('highlighted');
                        }
                    }, 3000);
                    break;
                }
            }
        }

        // 获取当前显示的指令列表
        function getCurrentDisplayedInstructions() {
            const filter = document.getElementById('filterInput').value.toLowerCase();
            const sortBy = document.getElementById('sortSelect').value;

            let currentInstructions = [...instructions];

            // 应用过滤
            if (filter) {
                currentInstructions = currentInstructions.filter(instr =>
                    instr.pc.toLowerCase().includes(filter) ||
                    instr.disassembly.toLowerCase().includes(filter)
                );
            }

            // 应用排序
            currentInstructions.sort((a, b) => {
                switch(sortBy) {
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }
            });

            return currentInstructions;
        }

        // 键盘快捷键处理
        function handleKeyPress(event) {
            // 如果用户正在输入框中输入，不处理快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {
                return;
            }

            const zoomSlider = document.getElementById('zoomSlider');
            const timelineContainer = document.querySelector('.timeline-container');
            const scrollStep = 50; // 滚动步长
            const zoomStep = 0.2; // 缩放步长

            switch(event.key.toLowerCase()) {
                case 'w': // 放大
                    event.preventDefault();
                    const newZoomUp = Math.min(parseFloat(zoomSlider.value) + zoomStep, 20);
                    zoomSlider.value = newZoomUp;
                    updateZoom();
                    break;

                case 's': // 缩小
                    event.preventDefault();
                    const newZoomDown = Math.max(parseFloat(zoomSlider.value) - zoomStep, 0.1);
                    zoomSlider.value = newZoomDown;
                    updateZoom();
                    break;

                case 'a': // 左移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.max(timelineContainer.scrollLeft - scrollStep, 0);
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'd': // 右移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.min(
                        timelineContainer.scrollLeft + scrollStep,
                        timelineContainer.scrollWidth - timelineContainer.clientWidth
                    );
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'r': // 复位缩放
                    event.preventDefault();
                    zoomSlider.value = 1;
                    updateZoom();
                    timelineContainer.scrollLeft = 0;
                    currentScrollLeft = 0;
                    break;
            }
        }

        // 事件监听器
        document.getElementById('zoomSlider').addEventListener('input', updateZoom);
        document.getElementById('filterInput').addEventListener('input', filterInstructions);
        document.getElementById('sortSelect').addEventListener('change', sortInstructions);

        // 键盘事件监听器
        document.addEventListener('keydown', handleKeyPress);

        // 初始化
        initTimeAxis();
        renderInstructions();
        initDragZoom();
    </script>
</body>
</html>
        
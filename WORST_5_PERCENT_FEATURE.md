# Worst 5% 执行时间指令功能说明

## 功能概述

在 `itrace_timeline_visualizer.py` 中新增了 **Worst 5% 执行时间指令** 统计功能，该功能可以：

1. **统计最慢的 5% 指令**：自动计算并显示执行时间最长的 5% 指令
2. **点击跳转功能**：点击任意 worst 5% 指令可以直接跳转到该指令在时间线中的位置
3. **自动缩放适配**：跳转时自动调整时间轴缩放，使目标指令占据合适的屏幕宽度
4. **垂直滚动定位**：整个网页的垂直滚动条也会跳转到目标指令位置
5. **居中显示**：目标指令会在屏幕中央高亮显示

## 实现的功能特性

### 1. Worst 5% 统计算法
- 按总执行时间（total_duration）对所有有效指令进行降序排序
- 计算 5% 的指令数量：`max(1, int(len(instructions) * 0.05))`
- 不设置最大数量限制，完全按照 5% 比例计算

### 2. 可视化展示
- 在页面底部添加了专门的 "Worst 5% 执行时间指令" 区域
- 每个指令显示：排名、PC地址、反汇编代码、执行时间
- 采用红色主题设计，突出显示性能瓶颈
- 鼠标悬停效果，提供良好的交互体验

### 3. 智能跳转功能
- **时间轴跳转**：水平滚动到目标指令的时间位置
- **自动缩放**：计算合适的缩放级别，使指令占据屏幕宽度的 40%
- **垂直滚动**：整个网页滚动到目标指令在指令列表中的位置
- **居中显示**：目标指令在视窗中央显示
- **高亮效果**：目标指令会有 3 秒的高亮动画效果

### 4. CSS 样式设计
```css
.worst-5-percent-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.worst-instruction-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    cursor: pointer;
    transition: all 0.3s ease;
}
```

## 使用方法

### 1. 生成包含 Worst 5% 功能的 HTML 文件
```bash
python itrace_timeline_visualizer.py your_itrace.log -o output.html
```

### 2. 在浏览器中使用
1. 打开生成的 HTML 文件
2. 滚动到页面底部查看 "Worst 5% 执行时间指令" 区域
3. 点击任意指令项，系统会自动：
   - 调整时间轴缩放到合适级别
   - 水平滚动到指令时间位置
   - 垂直滚动到指令在列表中的位置
   - 高亮显示目标指令

## 技术实现细节

### JavaScript 核心函数

1. **jumpToInstruction(instructionId)**：主跳转函数
2. **scrollToInstruction(instructionId)**：垂直滚动函数
3. **centerOnTime(targetTime)**：时间轴居中函数
4. **highlightInstruction(instructionId)**：高亮显示函数

### 关键算法

```javascript
// 计算合适的缩放级别
const targetWidthRatio = 0.4; // 目标宽度比例
const targetPixelWidth = panelWidth * targetWidthRatio;
const currentInstrPixelWidth = (instrDuration / timeRange) * baseWidth;
const requiredZoom = targetPixelWidth / currentInstrPixelWidth;

// 垂直滚动到目标位置
const targetOffsetTop = targetRect.top + window.pageYOffset;
const scrollPosition = targetOffsetTop - (windowHeight / 2) + (targetRect.height / 2);
window.scrollTo({
    top: Math.max(0, scrollPosition),
    behavior: 'smooth'
});
```

## 测试数据

提供了 `create_test_data()` 函数生成 100 条指令的测试数据，其中：
- 每 20 条指令中有 1 条特别慢（50-100 时间单位）
- 每 10 条指令中有 1 条比较慢（25-50 时间单位）
- 其他指令正常速度（5-25 时间单位）

这样可以很好地展示 Worst 5% 功能的效果。

## 文件说明

- `itrace_timeline_visualizer.py`：主程序文件，包含所有功能实现
- `test_itrace_large.log`：100 条指令的测试数据
- `itrace_timeline_final_with_scroll.html`：最终生成的 HTML 文件

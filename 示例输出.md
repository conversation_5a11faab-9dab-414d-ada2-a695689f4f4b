# ITrace统计信息提取器 - 示例输出

## HTML报告生成

脚本会生成美观的HTML报告页面：
- **文件名**: `itrace_statistics_summary.html`
- **特点**: 响应式设计、现代化UI、交互式表格

## HTML报告特性

### 1. 页面结构
- **头部**: 渐变色背景，显示标题和生成时间
- **统计摘要**: 卡片式布局显示关键指标
- **详细数据**: 表格形式展示所有有效的Test Case数据
- **页脚**: 显示扫描路径等信息

### 2. 数据过滤
- 自动过滤掉空的文件夹名（无效的Test Case）
- 只显示包含有效数据的Test Case
- 表格列头显示为"Test Case"而不是"文件夹名"

### 3. 视觉设计
- 现代化的卡片式布局
- 渐变色彩搭配
- 响应式设计，支持移动设备
- 悬停效果和阴影

## 控制台输出示例

```
开始扫描路径: D:\proj\sv_dump
找到 3 个itrace.log文件
处理: test_folder -> test_folder\itrace.log
  - 找到STALL STATISTICS: 断流百分比 20.00%
  - 找到UNIT UTILIZATION STATISTICS
处理: test_folder2 -> test_folder2\itrace.log
  - 找到STALL STATISTICS: 断流百分比 20.00%
  - 找到UNIT UTILIZATION STATISTICS
处理: simulation_run_1 -> simulation_run_1\itrace.log
  - 找到STALL STATISTICS: 断流百分比 20.00%
  - 找到UNIT UTILIZATION STATISTICS
HTML报告已生成: itrace_statistics_summary.html
共处理了 3 个文件夹的数据

=== 统计摘要 ===
包含STALL STATISTICS的文件: 3/3
包含UNIT UTILIZATION STATISTICS的文件: 3/3
断流百分比 - 平均: 20.00%, 最大: 20.00%, 最小: 20.00%
平均功能单元利用率:
  TMAC: 78.58%
  TSFU: 51.75%
  TALU: 65.42%
  TLD: 61.67%
  TST: 35.08%
```

## 数据说明

### 列含义

1. **Test Case**: itrace.log文件所在的文件夹名称，用作测试用例标识
2. **total_monitor_time**: 总监控时间（时间单位）
3. **total_stall_time**: 总断流时间（时间单位）
4. **stall_percentage**: 断流百分比，格式为"XX.XX%"
5. **total_tile_instructions**: 监控期间的tile指令总数
6. **tmac_utilization**: TMAC功能单元利用率，格式为"XX.XX%"
7. **tsfu_utilization**: TSFU功能单元利用率，格式为"XX.XX%"
8. **talu_utilization**: TALU功能单元利用率，格式为"XX.XX%"
9. **tld_utilization**: TLD功能单元利用率，格式为"XX.XX%"
10. **tst_utilization**: TST功能单元利用率，格式为"XX.XX%"

### 数据特点

- **百分比格式**: 所有百分比数据都以"XX.XX%"格式显示，便于直接阅读
- **精简列**: 移除了不必要的时间戳和周期计数列，只保留关键指标
- **完整性**: 对于缺失统计信息的文件，相应字段显示为空值
- **一致性**: 所有数值保持2位小数精度

### 适用场景

这种格式特别适合：
- 快速比较不同测试用例的性能
- 导入Excel进行进一步分析
- 生成性能报告和图表
- 识别性能异常和瓶颈

### Excel导入建议

1. 直接打开CSV文件，Excel会自动识别格式
2. 百分比列可以直接用于计算和图表制作
3. 可以使用条件格式高亮显示异常值
4. 支持数据透视表分析

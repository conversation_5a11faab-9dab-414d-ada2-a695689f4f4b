# ITrace统计信息提取器

## 功能概述

`itrace_statistics_extractor.py` 是一个Python脚本，用于遍历指定路径下所有文件夹中的`itrace.log`文件，提取其中的STALL STATISTICS和UNIT UTILIZATION STATISTICS信息，并汇总到一个Excel文件中，支持按日期分标签页管理。

## 主要功能

1. **递归遍历文件夹**: 自动搜索指定路径下所有子文件夹中的`itrace.log`文件
2. **提取统计信息**: 从每个`itrace.log`文件中提取两类统计信息：
   - **INSTRUCTION STALL STATISTICS**: 指令断流统计
   - **FUNCTIONAL UNIT UTILIZATION STATISTICS**: 功能单元利用率统计
3. **Excel汇总报告**: 将所有提取的数据汇总到一个Excel文件中，按日期分标签页管理
4. **智能文件管理**:
   - 如果文件不存在，创建新文件
   - 如果文件存在且有同日期标签页，覆盖该标签页
   - 如果文件存在但没有同日期标签页，新建标签页

## 使用方法

### 基本用法

```bash
# 扫描当前目录下所有itrace.log文件（使用默认Excel文件名）
python itrace_statistics_extractor.py
# 输出: itrace_statistics_summary.xlsx (标签页: 20250812)

# 指定扫描路径
python itrace_statistics_extractor.py --path /path/to/scan

# 指定输出文件名
python itrace_statistics_extractor.py --output my_report.xlsx

# 完整参数示例
python itrace_statistics_extractor.py --path ./simulation_results --output itrace_summary.xlsx --verbose --report
```

### 命令行参数

- `--path, -p`: 要扫描的根路径 (默认: 当前目录)
- `--output, -o`: 输出Excel文件名 (默认: itrace_statistics_summary.xlsx)
- `--verbose, -v`: 显示详细输出信息
- `--report, -r`: 生成详细的分析报告 (需要pandas库)

## 自动日期命名

脚本会自动使用当前系统日期来命名输出文件，格式为：`itrace_statistics_summary_YYYYMMDD.csv`

**示例：**
- 2025年8月12日运行：`itrace_statistics_summary_20250812.csv`
- 2025年12月25日运行：`itrace_statistics_summary_20251225.csv`

这样可以避免文件覆盖，方便按日期管理不同的分析结果。

## 输出格式

生成的CSV文件包含以下列：

### 基本信息
- `folder_name`: itrace.log所在的文件夹名

### STALL STATISTICS (指令断流统计)
- `total_monitor_time`: 总监控时间
- `total_stall_time`: 总断流时间
- `stall_percentage`: 断流百分比 (格式: XX.XX%)
- `total_tile_instructions`: tile指令总数

### UNIT UTILIZATION STATISTICS (功能单元利用率统计)
- `tmac_utilization`: TMAC利用率百分比 (格式: XX.XX%)
- `tsfu_utilization`: TSFU利用率百分比 (格式: XX.XX%)
- `talu_utilization`: TALU利用率百分比 (格式: XX.XX%)
- `tld_utilization`: TLD利用率百分比 (格式: XX.XX%)
- `tst_utilization`: TST利用率百分比 (格式: XX.XX%)

## 支持的统计信息格式

脚本能够识别以下格式的统计信息：

### STALL STATISTICS格式
```
// ===== INSTRUCTION STALL STATISTICS =====
// Stall monitor start time (0x9d02 fetch): 1000.00
// Stall monitor end time (thread_done): 5000.00
// Total monitor time: 4000.00
// Total stall time: 800.00
// Stall percentage: 20.00%
// Total tile instructions: 10
```

### UNIT UTILIZATION STATISTICS格式
```
// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====
// Total monitor cycles: 4000
// TMAC utilization: 75.50% (3020/4000 cycles)
// TSFU utilization: 45.25% (1810/4000 cycles)
// TALU utilization: 60.75% (2430/4000 cycles)
// TLD utilization: 55.00% (2200/4000 cycles)
// TST utilization: 30.25% (1210/4000 cycles)
```

## 错误处理

- 如果某个`itrace.log`文件不包含统计信息，对应的字段将为空值
- 如果文件读取失败，会显示错误信息但不会中断整个处理过程
- 脚本会显示处理进度和找到的统计信息概要

## 示例输出

```
开始扫描路径: /path/to/simulation/results
找到 3 个itrace.log文件
处理: test_case_1 -> test_case_1/itrace.log
  - 找到STALL STATISTICS: 断流百分比 20.00%
  - 找到UNIT UTILIZATION STATISTICS: 总监控周期 4000
处理: test_case_2 -> test_case_2/itrace.log
  - 找到STALL STATISTICS: 断流百分比 15.50%
  - 找到UNIT UTILIZATION STATISTICS: 总监控周期 6000
处理: test_case_3 -> test_case_3/itrace.log
  - 未找到STALL STATISTICS
  - 未找到UNIT UTILIZATION STATISTICS
CSV报告已生成: itrace_statistics_summary.csv
共处理了 3 个文件夹的数据
```

## 依赖要求

- Python 3.6+
- 标准库模块: `os`, `re`, `csv`, `argparse`, `pathlib`

## 注意事项

1. 脚本会递归搜索所有子目录中的`itrace.log`文件
2. 文件夹名用作数据索引，确保文件夹名具有唯一性和意义
3. 如果同一个文件夹名下有多个`itrace.log`文件，只会处理找到的第一个
4. CSV文件使用UTF-8编码，可以在Excel等工具中正常打开

## 高级功能

### 详细分析报告

使用 `--report` 参数可以生成详细的统计分析报告：

```bash
python itrace_statistics_extractor.py --report
```

这将生成一个文本报告文件，包含：
- 断流百分比的统计分析（平均值、中位数、最大值、最小值、标准差）
- 各功能单元利用率的统计分析
- 按断流百分比排序的文件夹列表

**注意**: 详细报告功能需要安装pandas库：
```bash
pip install pandas
```

### Verbose模式

使用 `--verbose` 参数可以显示更详细的处理信息：

```bash
python itrace_statistics_extractor.py --verbose
```

在verbose模式下，脚本会显示：
- 每个文件的完整路径
- 详细的统计信息发现情况
- 更多的调试信息

## 批处理使用

Windows用户可以使用提供的批处理文件：

```cmd
run_itrace_extractor.bat
```

该批处理文件提供交互式菜单，方便选择不同的操作模式。

## 数据分析建议

生成CSV文件后，可以使用以下工具进行进一步分析：

1. **Excel**: 直接打开CSV文件，使用数据透视表和图表功能
2. **Python pandas**: 进行更复杂的数据分析和可视化
3. **R**: 使用统计分析功能
4. **Tableau/Power BI**: 创建交互式仪表板

### Python分析示例

```python
import pandas as pd
import matplotlib.pyplot as plt

# 读取数据
df = pd.read_csv('itrace_statistics_summary.csv')

# 绘制断流百分比分布
plt.figure(figsize=(10, 6))
df['stall_percentage'].hist(bins=20)
plt.xlabel('Stall Percentage (%)')
plt.ylabel('Frequency')
plt.title('Distribution of Stall Percentages')
plt.show()

# 功能单元利用率对比
units = ['tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization']
df[units].boxplot(figsize=(12, 8))
plt.ylabel('Utilization (%)')
plt.title('Functional Unit Utilization Comparison')
plt.show()
```

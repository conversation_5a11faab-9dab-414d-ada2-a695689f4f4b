#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ITrace Timeline Visualizer的错误处理功能
"""

import os
import sys
import tempfile
from itrace_timeline_visualizer import ITraceTimelineVisualizer

def test_file_not_found():
    """测试文件不存在的情况"""
    print("=" * 50)
    print("测试1: 文件不存在")
    print("=" * 50)
    
    visualizer = ITraceTimelineVisualizer("nonexistent_file.log")
    result = visualizer.read_itrace_data()
    print(f"结果: {'通过' if not result else '失败'}")
    print()

def test_empty_file():
    """测试空文件"""
    print("=" * 50)
    print("测试2: 空文件")
    print("=" * 50)
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
        temp_file = f.name
    
    try:
        visualizer = ITraceTimelineVisualizer(temp_file)
        result = visualizer.read_itrace_data()
        print(f"结果: {'通过' if not result else '失败'}")
    finally:
        os.unlink(temp_file)
    print()

def test_invalid_csv_format():
    """测试无效的CSV格式"""
    print("=" * 50)
    print("测试3: 无效CSV格式")
    print("=" * 50)
    
    invalid_csv = """InstrID,PC,Instruction
1,0x1000,invalid
2,0x1008,missing,columns,here
3,0x1010"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log', encoding='utf-8') as f:
        f.write(invalid_csv)
        temp_file = f.name
    
    try:
        visualizer = ITraceTimelineVisualizer(temp_file)
        result = visualizer.read_itrace_data()
        print(f"结果: {'通过' if not result else '失败'}")
    finally:
        os.unlink(temp_file)
    print()

def test_missing_columns():
    """测试缺少必要列"""
    print("=" * 50)
    print("测试4: 缺少必要列")
    print("=" * 50)
    
    missing_columns_csv = """InstrID,PC,Instruction
1,0x1000,test1
2,0x1008,test2"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log', encoding='utf-8') as f:
        f.write(missing_columns_csv)
        temp_file = f.name
    
    try:
        visualizer = ITraceTimelineVisualizer(temp_file)
        result = visualizer.read_itrace_data()
        print(f"结果: {'通过' if not result else '失败'}")
    finally:
        os.unlink(temp_file)
    print()

def test_invalid_data_types():
    """测试无效的数据类型"""
    print("=" * 50)
    print("测试5: 无效数据类型")
    print("=" * 50)
    
    invalid_data_csv = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
invalid_id,0x1000,test1,disasm1,1000,1005,1010,1015,1020
2,0x1008,test2,disasm2,invalid_time,1030,1035,1040,1045
3,0x1010,test3,disasm3,1050,1055,1060,1065,1070"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log', encoding='utf-8') as f:
        f.write(invalid_data_csv)
        temp_file = f.name
    
    try:
        visualizer = ITraceTimelineVisualizer(temp_file)
        result = visualizer.read_itrace_data()
        print(f"结果: {'通过' if not result else '失败'}")
    finally:
        os.unlink(temp_file)
    print()

def test_valid_data_with_errors():
    """测试包含部分错误的有效数据"""
    print("=" * 50)
    print("测试6: 包含部分错误的有效数据")
    print("=" * 50)
    
    mixed_data_csv = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x1000,test1,disasm1,1000,1005,1010,1015,1020
invalid_id,0x1008,test2,disasm2,1025,1030,1035,1040,1045
3,0x1010,test3,disasm3,1050,1055,1060,1065,1070
4,0x1018,test4,disasm4,invalid_time,1075,1080,1085,1090
5,0x1020,test5,disasm5,1095,1100,1105,1110,1115"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log', encoding='utf-8') as f:
        f.write(mixed_data_csv)
        temp_file = f.name
    
    try:
        visualizer = ITraceTimelineVisualizer(temp_file)
        if visualizer.read_itrace_data():
            instructions = visualizer.process_instruction_data()
            print(f"成功处理的指令数: {len(instructions)}")
            print(f"结果: {'通过' if len(instructions) > 0 else '失败'}")
        else:
            print("结果: 失败")
    finally:
        os.unlink(temp_file)
    print()

def test_permission_error():
    """测试权限错误（仅在支持的系统上）"""
    print("=" * 50)
    print("测试7: 权限错误")
    print("=" * 50)
    
    # 创建一个只读文件
    valid_csv = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x1000,test1,disasm1,1000,1005,1010,1015,1020"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log', encoding='utf-8') as f:
        f.write(valid_csv)
        temp_file = f.name
    
    try:
        # 尝试设置为只读（在Windows上可能不起作用）
        try:
            os.chmod(temp_file, 0o444)
        except:
            print("无法设置文件权限，跳过此测试")
            return
        
        visualizer = ITraceTimelineVisualizer(temp_file)
        if visualizer.read_itrace_data():
            instructions = visualizer.process_instruction_data()
            # 尝试写入到只读目录
            result = visualizer.create_visualization("/root/test_output.html")
            print(f"结果: {'通过' if not result else '失败'}")
        else:
            print("结果: 数据读取失败")
    finally:
        try:
            os.chmod(temp_file, 0o666)  # 恢复权限以便删除
            os.unlink(temp_file)
        except:
            pass
    print()

def test_complete_workflow():
    """测试完整的工作流程"""
    print("=" * 50)
    print("测试8: 完整工作流程")
    print("=" * 50)
    
    valid_csv = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log', encoding='utf-8') as f:
        f.write(valid_csv)
        temp_file = f.name
    
    output_file = temp_file.replace('.log', '_timeline.html')
    
    try:
        visualizer = ITraceTimelineVisualizer(temp_file)
        result = visualizer.create_visualization(output_file)
        
        if result and os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"生成的HTML文件大小: {file_size} 字节")
            print(f"结果: {'通过' if file_size > 0 else '失败'}")
        else:
            print("结果: 失败")
    finally:
        try:
            os.unlink(temp_file)
            if os.path.exists(output_file):
                os.unlink(output_file)
        except:
            pass
    print()

def main():
    """运行所有测试"""
    print("ITrace Timeline Visualizer 错误处理测试")
    print("=" * 60)
    
    test_file_not_found()
    test_empty_file()
    test_invalid_csv_format()
    test_missing_columns()
    test_invalid_data_types()
    test_valid_data_with_errors()
    test_permission_error()
    test_complete_workflow()
    
    print("=" * 60)
    print("所有测试完成")

if __name__ == "__main__":
    main()

InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000002000,0x00000000fc123456,"tld.trii.linear.u64.global t7, (x12)",2000,2005,2010,2015,2025
2,0x000000002008,0x00000000fc234567,"tmma.ntt t5, t3, t4",2030,2035,2040,2045,2060
3,0x000000002010,0x00000000fc345678,"tcsrw.i 0x8",2065,2070,2075,2080,2085

// ===== INSTRUCTION STALL STATISTICS =====
// Stall monitor start time (0x9d02 fetch): 2000.00
// Stall monitor end time (thread_done): 8000.00
// Total monitor time: 6000.00
// Total stall time: 1200.00
// Stall percentage: 20.00%
// Total tile instructions: 15

// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====
// Total monitor cycles: 6000
// TMAC utilization: 80.25% (4815/6000 cycles)
// TSFU utilization: 50.75% (3045/6000 cycles)
// TALU utilization: 65.50% (3930/6000 cycles)
// TLD utilization: 70.00% (4200/6000 cycles)
// TST utilization: 35.25% (2115/6000 cycles)

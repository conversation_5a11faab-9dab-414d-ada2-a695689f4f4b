# ITrace统计信息提取器项目总结

## 项目概述

本项目成功创建了一个Python脚本，用于遍历指定路径下所有文件夹中的`itrace.log`文件，提取其中的STALL STATISTICS和UNIT UTILIZATION STATISTICS信息，并汇总到CSV文件中。

## 已完成的功能

### 1. 核心提取器 (`itrace_statistics_extractor.py`)

**主要功能:**
- ✅ 递归遍历文件夹查找`itrace.log`文件
- ✅ 提取INSTRUCTION STALL STATISTICS信息
- ✅ 提取FUNCTIONAL UNIT UTILIZATION STATISTICS信息
- ✅ 生成CSV汇总报告
- ✅ 以文件夹名作为数据索引
- ✅ 支持verbose模式显示详细信息
- ✅ 支持生成详细分析报告（需要pandas）

**支持的统计信息:**

**STALL STATISTICS:**
- 断流监控开始/结束时间
- 总监控时间和断流时间
- 断流百分比
- tile指令总数

**UNIT UTILIZATION STATISTICS:**
- 总监控周期数
- 各功能单元(TMAC, TSFU, TALU, TLD, TST)的利用率
- 各功能单元的活跃周期数和总周期数

### 2. 数据分析工具 (`analyze_itrace_data.py`)

**分析功能:**
- ✅ 断流统计分析（平均值、中位数、异常值检测）
- ✅ 功能单元利用率分析
- ✅ 可视化图表生成（直方图、箱线图、散点图）
- ✅ 排序和排名分析

### 3. 辅助工具

- ✅ Windows批处理文件 (`run_itrace_extractor.bat`)
- ✅ 测试脚本 (`test_extractor.py`)
- ✅ 详细使用文档 (`README_itrace_extractor.md`)

## 文件结构

```
项目文件/
├── itrace_statistics_extractor.py    # 主提取器脚本
├── analyze_itrace_data.py            # 数据分析脚本
├── test_extractor.py                 # 测试脚本
├── run_itrace_extractor.bat          # Windows批处理文件
├── README_itrace_extractor.md        # 详细使用文档
├── 项目总结.md                       # 本文件
├── test_folder/itrace.log            # 测试数据1
├── test_folder2/itrace.log           # 测试数据2
└── *.csv                             # 生成的报告文件
```

## 使用示例

### 基本使用
```bash
# 扫描当前目录
python itrace_statistics_extractor.py

# 指定路径和输出文件
python itrace_statistics_extractor.py --path ./simulation_results --output report.csv

# 详细模式
python itrace_statistics_extractor.py --verbose --report
```

### 数据分析
```bash
# 基本分析
python analyze_itrace_data.py report.csv

# 生成可视化图表
python analyze_itrace_data.py report.csv --plot
```

## 输出格式

### CSV文件列结构
- `folder_name`: 文件夹名（索引）
- STALL统计: `stall_start_time`, `stall_end_time`, `total_monitor_time`, `total_stall_time`, `stall_percentage`, `total_tile_instructions`
- UNIT统计: `total_monitor_cycles`, `{unit}_utilization`, `{unit}_active_cycles`, `{unit}_total_cycles` (对于TMAC, TSFU, TALU, TLD, TST)

### 示例输出
```
开始扫描路径: /path/to/simulation
找到 3 个itrace.log文件
处理: test_case_1
  - STALL: 20.00%
  - UNIT: 4000 周期
处理: test_case_2
  - STALL: 15.50%
  - UNIT: 6000 周期
CSV报告已生成: itrace_statistics_summary.csv
共处理了 3 个文件夹的数据

=== 统计摘要 ===
包含STALL STATISTICS的文件: 2/3
包含UNIT UTILIZATION STATISTICS的文件: 2/3
断流百分比 - 平均: 17.75%, 最大: 20.00%, 最小: 15.50%
平均功能单元利用率:
  TMAC: 77.88%
  TSFU: 48.00%
  TALU: 63.13%
  TLD: 62.50%
  TST: 32.75%
```

## 技术特点

### 1. 鲁棒性
- 错误处理：文件读取失败不会中断整个处理过程
- 缺失数据处理：没有统计信息的文件会填入空值
- 编码支持：使用UTF-8编码确保兼容性

### 2. 灵活性
- 支持任意深度的目录遍历
- 可配置的输出文件名和路径
- 可选的详细输出模式

### 3. 扩展性
- 模块化设计，易于添加新的统计信息类型
- 正则表达式模式易于修改和扩展
- 支持生成多种格式的报告

## 依赖要求

### 基本功能
- Python 3.6+
- 标准库：`os`, `re`, `csv`, `argparse`, `pathlib`

### 高级功能
- `pandas`: 详细报告生成
- `matplotlib`, `seaborn`: 数据可视化

## 测试验证

✅ 已通过以下测试：
1. 基本功能测试：正确提取统计信息
2. 边界情况测试：处理缺失统计信息的文件
3. 路径遍历测试：正确遍历多层目录结构
4. 输出格式测试：生成正确的CSV格式
5. 命令行参数测试：所有参数正常工作

## 使用建议

1. **批量处理**: 适用于处理大量仿真结果文件夹
2. **性能监控**: 可用于跟踪不同配置下的性能变化
3. **问题诊断**: 通过断流百分比和利用率数据识别性能瓶颈
4. **报告生成**: 自动化生成性能分析报告

## 后续扩展可能

1. 支持更多统计信息类型
2. 添加实时监控功能
3. 集成到CI/CD流程中
4. 支持更多输出格式（JSON, XML等）
5. 添加Web界面
6. 支持数据库存储

## 总结

本项目成功实现了用户的所有需求，提供了一个完整、易用、可扩展的ITrace统计信息提取和分析解决方案。脚本具有良好的错误处理能力和用户友好的输出格式，可以有效地帮助用户分析大量的仿真结果数据。

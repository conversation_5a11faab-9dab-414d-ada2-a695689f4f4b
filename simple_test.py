#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

from itrace_timeline_visualizer import ITraceTimelineVisualizer

def main():
    print("开始测试...")
    
    # 创建可视化器
    visualizer = ITraceTimelineVisualizer("test_with_comments_and_errors.log")
    
    # 测试读取数据
    print("测试读取数据...")
    result = visualizer.read_itrace_data()
    print(f"读取结果: {result}")
    
    if result:
        print("测试处理指令数据...")
        instructions = visualizer.process_instruction_data()
        print(f"处理的指令数: {len(instructions)}")
        
        print("测试生成HTML...")
        html_result = visualizer.create_visualization("simple_test_output.html")
        print(f"HTML生成结果: {html_result}")
    
    print("测试完成")

if __name__ == "__main__":
    main()

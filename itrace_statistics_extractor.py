#!/usr/bin/env python3
"""
ITrace统计信息提取器
遍历指定路径下所有文件夹中的itrace.log文件，提取STALL STATISTICS和UNIT UTILIZATION STATISTICS信息，
并汇总到CSV文件中。
"""

import os
import re
import csv
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple


class ITraceStatsExtractor:
    def __init__(self):
        self.stall_stats_pattern = re.compile(
            r'// ===== INSTRUCTION STALL STATISTICS =====\s*\n'
            r'// Stall monitor start time \(0x9d02 fetch\): ([\d.]+)\s*\n'
            r'// Stall monitor end time \(thread_done\): ([\d.]+)\s*\n'
            r'// Total monitor time: ([\d.]+)\s*\n'
            r'// Total stall time: ([\d.]+)\s*\n'
            r'// Stall percentage: ([\d.]+)%\s*\n'
            r'// Total tile instructions: (\d+)',
            re.MULTILINE
        )
        
        self.unit_util_pattern = re.compile(
            r'// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====\s*\n'
            r'// Total monitor cycles: (\d+)\s*\n'
            r'// TMAC utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TSFU utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TALU utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TLD utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TST utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)',
            re.MULTILINE
        )

    def find_itrace_files(self, root_path: str) -> List[Tuple[str, str]]:
        """
        遍历指定路径下所有文件夹，查找itrace.log文件
        返回: [(文件夹名, itrace.log完整路径), ...]
        """
        itrace_files = []
        root_path = Path(root_path)
        
        for item in root_path.rglob("itrace.log"):
            folder_name = item.parent.name
            itrace_files.append((folder_name, str(item)))
        
        return itrace_files

    def extract_stall_statistics(self, content: str) -> Optional[Dict]:
        """提取STALL STATISTICS信息"""
        match = self.stall_stats_pattern.search(content)
        if match:
            return {
                'stall_start_time': float(match.group(1)),
                'stall_end_time': float(match.group(2)),
                'total_monitor_time': float(match.group(3)),
                'total_stall_time': float(match.group(4)),
                'stall_percentage': float(match.group(5)),
                'total_tile_instructions': int(match.group(6))
            }
        return None

    def extract_unit_utilization(self, content: str) -> Optional[Dict]:
        """提取UNIT UTILIZATION STATISTICS信息"""
        match = self.unit_util_pattern.search(content)
        if match:
            return {
                'total_monitor_cycles': int(match.group(1)),
                'tmac_utilization': float(match.group(2)),
                'tmac_active_cycles': int(match.group(3)),
                'tmac_total_cycles': int(match.group(4)),
                'tsfu_utilization': float(match.group(5)),
                'tsfu_active_cycles': int(match.group(6)),
                'tsfu_total_cycles': int(match.group(7)),
                'talu_utilization': float(match.group(8)),
                'talu_active_cycles': int(match.group(9)),
                'talu_total_cycles': int(match.group(10)),
                'tld_utilization': float(match.group(11)),
                'tld_active_cycles': int(match.group(12)),
                'tld_total_cycles': int(match.group(13)),
                'tst_utilization': float(match.group(14)),
                'tst_active_cycles': int(match.group(15)),
                'tst_total_cycles': int(match.group(16))
            }
        return None

    def process_itrace_file(self, file_path: str) -> Tuple[Optional[Dict], Optional[Dict]]:
        """处理单个itrace.log文件，提取统计信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            stall_stats = self.extract_stall_statistics(content)
            unit_stats = self.extract_unit_utilization(content)
            
            return stall_stats, unit_stats
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return None, None

    def generate_csv_report(self, results: List[Dict], output_file: str):
        """生成CSV报告"""
        if not results:
            print("没有找到任何统计数据")
            return

        # 定义CSV列头
        fieldnames = [
            'folder_name',
            # STALL STATISTICS (移除了stall_start_time, stall_end_time)
            'total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions',
            # UNIT UTILIZATION STATISTICS (移除了total_monitor_cycles和各单元的active/total cycles)
            'tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization'
        ]

        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                # 格式化百分比数据
                formatted_result = result.copy()

                # 格式化stall_percentage
                if formatted_result.get('stall_percentage') is not None:
                    formatted_result['stall_percentage'] = f"{formatted_result['stall_percentage']:.2f}%"

                # 格式化各功能单元利用率
                for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                    util_key = f'{unit}_utilization'
                    if formatted_result.get(util_key) is not None:
                        formatted_result[util_key] = f"{formatted_result[util_key]:.2f}%"

                writer.writerow(formatted_result)

        print(f"CSV报告已生成: {output_file}")
        print(f"共处理了 {len(results)} 个文件夹的数据")

        # 生成统计摘要
        self.print_summary(results)

    def print_summary(self, results: List[Dict]):
        """打印统计摘要"""
        print("\n=== 统计摘要 ===")

        stall_data = [r for r in results if r.get('stall_percentage') is not None]
        unit_data = [r for r in results if r.get('tmac_utilization') is not None]

        print(f"包含STALL STATISTICS的文件: {len(stall_data)}/{len(results)}")
        print(f"包含UNIT UTILIZATION STATISTICS的文件: {len(unit_data)}/{len(results)}")

        if stall_data:
            avg_stall = sum(r['stall_percentage'] for r in stall_data) / len(stall_data)
            max_stall = max(r['stall_percentage'] for r in stall_data)
            min_stall = min(r['stall_percentage'] for r in stall_data)
            print(f"断流百分比 - 平均: {avg_stall:.2f}%, 最大: {max_stall:.2f}%, 最小: {min_stall:.2f}%")

        if unit_data:
            avg_tmac = sum(r['tmac_utilization'] for r in unit_data) / len(unit_data)
            avg_tsfu = sum(r['tsfu_utilization'] for r in unit_data) / len(unit_data)
            avg_talu = sum(r['talu_utilization'] for r in unit_data) / len(unit_data)
            avg_tld = sum(r['tld_utilization'] for r in unit_data) / len(unit_data)
            avg_tst = sum(r['tst_utilization'] for r in unit_data) / len(unit_data)
            print(f"平均功能单元利用率:")
            print(f"  TMAC: {avg_tmac:.2f}%")
            print(f"  TSFU: {avg_tsfu:.2f}%")
            print(f"  TALU: {avg_talu:.2f}%")
            print(f"  TLD: {avg_tld:.2f}%")
            print(f"  TST: {avg_tst:.2f}%")

    def run(self, root_path: str = ".", output_file: str = "itrace_statistics_summary.csv"):
        """主执行函数"""
        print(f"开始扫描路径: {os.path.abspath(root_path)}")
        
        # 查找所有itrace.log文件
        itrace_files = self.find_itrace_files(root_path)
        print(f"找到 {len(itrace_files)} 个itrace.log文件")
        
        if not itrace_files:
            print("未找到任何itrace.log文件")
            return

        results = []
        
        for folder_name, file_path in itrace_files:
            print(f"处理: {folder_name} -> {file_path}")
            
            stall_stats, unit_stats = self.process_itrace_file(file_path)
            
            # 创建结果记录
            result = {'folder_name': folder_name}
            
            # 添加STALL STATISTICS数据 (移除了stall_start_time, stall_end_time)
            if stall_stats:
                for key in ['total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions']:
                    if key in stall_stats:
                        result[key] = stall_stats[key]
                print(f"  - 找到STALL STATISTICS: 断流百分比 {stall_stats['stall_percentage']:.2f}%")
            else:
                print(f"  - 未找到STALL STATISTICS")
                # 添加空值 (移除了stall_start_time, stall_end_time)
                for key in ['total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions']:
                    result[key] = None
            
            # 添加UNIT UTILIZATION STATISTICS数据
            if unit_stats:
                # 只添加利用率数据，不包括cycles数据
                for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                    util_key = f'{unit}_utilization'
                    if util_key in unit_stats:
                        result[util_key] = unit_stats[util_key]
                print(f"  - 找到UNIT UTILIZATION STATISTICS")
            else:
                print(f"  - 未找到UNIT UTILIZATION STATISTICS")
                # 添加空值 (移除了total_monitor_cycles和各单元的active/total cycles)
                for key in ['tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization']:
                    result[key] = None
            
            results.append(result)

        # 生成CSV报告
        self.generate_csv_report(results, output_file)


def main():
    parser = argparse.ArgumentParser(description='提取itrace.log文件中的统计信息并汇总到CSV')
    parser.add_argument('--path', '-p', default='.',
                       help='要扫描的根路径 (默认: 当前目录)')
    parser.add_argument('--output', '-o', default='itrace_statistics_summary.csv',
                       help='输出CSV文件名 (默认: itrace_statistics_summary.csv)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出信息')
    parser.add_argument('--report', '-r', action='store_true',
                       help='生成详细的分析报告')

    args = parser.parse_args()

    extractor = ITraceStatsExtractor()
    extractor.verbose = args.verbose
    extractor.run(args.path, args.output)

    if args.report:
        report_file = args.output.replace('.csv', '_report.txt')
        extractor.generate_detailed_report(args.output, report_file)


if __name__ == "__main__":
    main()

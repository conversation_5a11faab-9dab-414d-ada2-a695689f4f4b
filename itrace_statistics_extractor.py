#!/usr/bin/env python3
"""
ITrace统计信息提取器
遍历指定路径下所有文件夹中的itrace.log文件，提取STALL STATISTICS和UNIT UTILIZATION STATISTICS信息，
并汇总到CSV文件中。
"""

import os
import re
import csv
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 尝试导入pandas和openpyxl，如果失败则提供备选方案
try:
    import pandas as pd
    EXCEL_SUPPORT = True
except ImportError:
    EXCEL_SUPPORT = False


class ITraceStatsExtractor:
    def __init__(self):
        self.stall_stats_pattern = re.compile(
            r'// ===== INSTRUCTION STALL STATISTICS =====\s*\n'
            r'// Stall monitor start time \(0x9d02 fetch\): ([\d.]+)\s*\n'
            r'// Stall monitor end time \(thread_done\): ([\d.]+)\s*\n'
            r'// Total monitor time: ([\d.]+)\s*\n'
            r'// Total stall time: ([\d.]+)\s*\n'
            r'// Stall percentage: ([\d.]+)%\s*\n'
            r'// Total tile instructions: (\d+)',
            re.MULTILINE
        )
        
        self.unit_util_pattern = re.compile(
            r'// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====\s*\n'
            r'// Total monitor cycles: (\d+)\s*\n'
            r'// TMAC utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TSFU utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TALU utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TLD utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)\s*\n'
            r'// TST utilization: ([\d.]+)% \((\d+)/(\d+) cycles\)',
            re.MULTILINE
        )

    def find_itrace_files(self, root_path: str) -> List[Tuple[str, str]]:
        """
        遍历指定路径下所有文件夹，查找itrace.log文件
        返回: [(文件夹名, itrace.log完整路径), ...]
        """
        itrace_files = []
        root_path = Path(root_path)
        
        for item in root_path.rglob("itrace.log"):
            folder_name = item.parent.name
            itrace_files.append((folder_name, str(item)))
        
        return itrace_files

    def extract_stall_statistics(self, content: str) -> Optional[Dict]:
        """提取STALL STATISTICS信息"""
        match = self.stall_stats_pattern.search(content)
        if match:
            return {
                'stall_start_time': float(match.group(1)),
                'stall_end_time': float(match.group(2)),
                'total_monitor_time': float(match.group(3)),
                'total_stall_time': float(match.group(4)),
                'stall_percentage': float(match.group(5)),
                'total_tile_instructions': int(match.group(6))
            }
        return None

    def extract_unit_utilization(self, content: str) -> Optional[Dict]:
        """提取UNIT UTILIZATION STATISTICS信息"""
        match = self.unit_util_pattern.search(content)
        if match:
            return {
                'total_monitor_cycles': int(match.group(1)),
                'tmac_utilization': float(match.group(2)),
                'tmac_active_cycles': int(match.group(3)),
                'tmac_total_cycles': int(match.group(4)),
                'tsfu_utilization': float(match.group(5)),
                'tsfu_active_cycles': int(match.group(6)),
                'tsfu_total_cycles': int(match.group(7)),
                'talu_utilization': float(match.group(8)),
                'talu_active_cycles': int(match.group(9)),
                'talu_total_cycles': int(match.group(10)),
                'tld_utilization': float(match.group(11)),
                'tld_active_cycles': int(match.group(12)),
                'tld_total_cycles': int(match.group(13)),
                'tst_utilization': float(match.group(14)),
                'tst_active_cycles': int(match.group(15)),
                'tst_total_cycles': int(match.group(16))
            }
        return None

    def process_itrace_file(self, file_path: str) -> Tuple[Optional[Dict], Optional[Dict]]:
        """处理单个itrace.log文件，提取统计信息"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            stall_stats = self.extract_stall_statistics(content)
            unit_stats = self.extract_unit_utilization(content)
            
            return stall_stats, unit_stats
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return None, None

    def generate_excel_report(self, results: List[Dict], output_file: str):
        """生成Excel报告，支持按日期分标签页"""
        if not results:
            print("没有找到任何统计数据")
            return

        # 检查Excel支持
        if not EXCEL_SUPPORT:
            print("警告: 未安装pandas或openpyxl库，无法生成Excel文件")
            print("请运行: pip install pandas openpyxl")
            print("将生成CSV文件作为备选...")
            csv_file = output_file.replace('.xlsx', '.csv')
            self.generate_csv_fallback(results, csv_file)
            return

        # 获取当前日期作为标签页名
        current_date = datetime.now().strftime("%Y%m%d")

        # 准备数据
        data_rows = []
        for result in results:
            # 格式化百分比数据
            formatted_result = result.copy()

            # 格式化stall_percentage
            if formatted_result.get('stall_percentage') is not None:
                formatted_result['stall_percentage'] = f"{formatted_result['stall_percentage']:.2f}%"

            # 格式化各功能单元利用率
            for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                util_key = f'{unit}_utilization'
                if formatted_result.get(util_key) is not None:
                    formatted_result[util_key] = f"{formatted_result[util_key]:.2f}%"

            data_rows.append(formatted_result)

        # 创建DataFrame
        df = pd.DataFrame(data_rows)

        # 确保列顺序
        column_order = [
            'folder_name',
            'total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions',
            'tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization'
        ]
        df = df.reindex(columns=column_order)

        # 处理Excel文件
        try:
            if os.path.exists(output_file):
                # 文件存在，读取现有数据
                print(f"文件 {output_file} 已存在，检查标签页...")

                # 读取现有的Excel文件
                existing_sheets = pd.read_excel(output_file, sheet_name=None)

                if current_date in existing_sheets:
                    print(f"标签页 '{current_date}' 已存在，将覆盖该标签页")
                else:
                    print(f"创建新标签页 '{current_date}'")

                # 更新或添加当前日期的标签页
                existing_sheets[current_date] = df

                # 写回Excel文件
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    for sheet_name, sheet_df in existing_sheets.items():
                        sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

            else:
                # 文件不存在，创建新文件
                print(f"创建新文件 {output_file}，标签页名: '{current_date}'")
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name=current_date, index=False)

            print(f"Excel报告已生成: {output_file}")
            print(f"标签页: {current_date}")
            print(f"共处理了 {len(results)} 个文件夹的数据")

        except Exception as e:
            print(f"生成Excel报告时出错: {e}")
            print("尝试生成CSV格式作为备选...")
            # 备选方案：生成CSV文件
            csv_file = output_file.replace('.xlsx', '.csv')
            self.generate_csv_fallback(results, csv_file)

        # 生成统计摘要
        self.print_summary(results)

    def generate_csv_fallback(self, results: List[Dict], output_file: str):
        """备选方案：生成CSV报告"""
        fieldnames = [
            'folder_name',
            'total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions',
            'tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization'
        ]

        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                # 格式化百分比数据
                formatted_result = result.copy()

                # 格式化stall_percentage
                if formatted_result.get('stall_percentage') is not None:
                    formatted_result['stall_percentage'] = f"{formatted_result['stall_percentage']:.2f}%"

                # 格式化各功能单元利用率
                for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                    util_key = f'{unit}_utilization'
                    if formatted_result.get(util_key) is not None:
                        formatted_result[util_key] = f"{formatted_result[util_key]:.2f}%"

                writer.writerow(formatted_result)

        print(f"CSV报告已生成: {output_file}")

    def generate_html_report(self, results: List[Dict], output_file: str):
        """生成HTML报告"""
        if not results:
            print("没有找到任何统计数据")
            return

        # 获取当前日期和时间
        current_datetime = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")

        # 准备数据
        data_rows = []
        for result in results:
            # 格式化百分比数据
            formatted_result = result.copy()

            # 格式化stall_percentage
            if formatted_result.get('stall_percentage') is not None:
                formatted_result['stall_percentage'] = f"{formatted_result['stall_percentage']:.2f}%"

            # 格式化各功能单元利用率
            for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                util_key = f'{unit}_utilization'
                if formatted_result.get(util_key) is not None:
                    formatted_result[util_key] = f"{formatted_result[util_key]:.2f}%"

            data_rows.append(formatted_result)

        # 计算统计摘要
        stall_data = [r for r in results if r.get('stall_percentage') is not None]
        unit_data = [r for r in results if r.get('tmac_utilization') is not None]

        summary_stats = {}
        if stall_data:
            summary_stats['stall'] = {
                'count': len(stall_data),
                'avg': sum(r['stall_percentage'] for r in stall_data) / len(stall_data),
                'max': max(r['stall_percentage'] for r in stall_data),
                'min': min(r['stall_percentage'] for r in stall_data)
            }

        if unit_data:
            summary_stats['units'] = {}
            for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                util_key = f'{unit}_utilization'
                if util_key in unit_data[0]:
                    avg_util = sum(r[util_key] for r in unit_data) / len(unit_data)
                    summary_stats['units'][unit] = avg_util

        # 生成HTML内容
        html_content = self.create_html_content(data_rows, summary_stats, current_datetime, len(results))

        # 写入文件
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"HTML报告已生成: {output_file}")
            print(f"生成时间: {current_datetime}")
            print(f"共处理了 {len(results)} 个文件夹的数据")
        except Exception as e:
            print(f"生成HTML报告时出错: {e}")

        # 生成统计摘要
        self.print_summary(results)

    def create_html_content(self, data_rows: List[Dict], summary_stats: Dict, timestamp: str, total_count: int) -> str:
        """创建HTML内容"""

        # HTML模板
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace统计信息报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .summary {{
            padding: 30px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }}
        .summary h2 {{
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }}
        .stat-card h3 {{
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 1.1em;
        }}
        .stat-value {{
            font-size: 1.8em;
            font-weight: bold;
            color: #667eea;
            margin: 5px 0;
        }}
        .stat-label {{
            color: #6c757d;
            font-size: 0.9em;
        }}
        .data-section {{
            padding: 30px;
        }}
        .data-section h2 {{
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }}
        .table-container {{
            overflow-x: auto;
            margin-top: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            background: white;
        }}
        th {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 10px;
            text-align: left;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        td {{
            padding: 12px 10px;
            border-bottom: 1px solid #dee2e6;
        }}
        tr:hover {{
            background-color: #f8f9fa;
        }}
        .percentage {{
            font-weight: bold;
            color: #28a745;
        }}
        .folder-name {{
            font-weight: bold;
            color: #495057;
        }}
        .no-data {{
            color: #6c757d;
            font-style: italic;
        }}
        .footer {{
            padding: 20px 30px;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }}
        @media (max-width: 768px) {{
            .container {{
                margin: 10px;
                border-radius: 0;
            }}
            .header, .summary, .data-section {{
                padding: 20px;
            }}
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
            table {{
                font-size: 0.9em;
            }}
            th, td {{
                padding: 8px 6px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ITrace统计信息报告</h1>
            <p>生成时间: {timestamp}</p>
        </div>

        <div class="summary">
            <h2>📊 统计摘要</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>总文件夹数</h3>
                    <div class="stat-value">{total_count}</div>
                    <div class="stat-label">个文件夹</div>
                </div>
                {self._generate_summary_cards(summary_stats)}
            </div>
        </div>

        <div class="data-section">
            <h2>📋 详细数据</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>文件夹名</th>
                            <th>总监控时间</th>
                            <th>总断流时间</th>
                            <th>断流百分比</th>
                            <th>Tile指令数</th>
                            <th>TMAC利用率</th>
                            <th>TSFU利用率</th>
                            <th>TALU利用率</th>
                            <th>TLD利用率</th>
                            <th>TST利用率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {self._generate_table_rows(data_rows)}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="footer">
            <p>由 ITrace统计信息提取器 生成 | 数据来源: itrace.log文件</p>
        </div>
    </div>
</body>
</html>
"""
        return html_template

    def print_summary(self, results: List[Dict]):
        """打印统计摘要"""
        print("\n=== 统计摘要 ===")

        stall_data = [r for r in results if r.get('stall_percentage') is not None]
        unit_data = [r for r in results if r.get('tmac_utilization') is not None]

        print(f"包含STALL STATISTICS的文件: {len(stall_data)}/{len(results)}")
        print(f"包含UNIT UTILIZATION STATISTICS的文件: {len(unit_data)}/{len(results)}")

        if stall_data:
            avg_stall = sum(r['stall_percentage'] for r in stall_data) / len(stall_data)
            max_stall = max(r['stall_percentage'] for r in stall_data)
            min_stall = min(r['stall_percentage'] for r in stall_data)
            print(f"断流百分比 - 平均: {avg_stall:.2f}%, 最大: {max_stall:.2f}%, 最小: {min_stall:.2f}%")

        if unit_data:
            avg_tmac = sum(r['tmac_utilization'] for r in unit_data) / len(unit_data)
            avg_tsfu = sum(r['tsfu_utilization'] for r in unit_data) / len(unit_data)
            avg_talu = sum(r['talu_utilization'] for r in unit_data) / len(unit_data)
            avg_tld = sum(r['tld_utilization'] for r in unit_data) / len(unit_data)
            avg_tst = sum(r['tst_utilization'] for r in unit_data) / len(unit_data)
            print(f"平均功能单元利用率:")
            print(f"  TMAC: {avg_tmac:.2f}%")
            print(f"  TSFU: {avg_tsfu:.2f}%")
            print(f"  TALU: {avg_talu:.2f}%")
            print(f"  TLD: {avg_tld:.2f}%")
            print(f"  TST: {avg_tst:.2f}%")

    def run(self, root_path: str = ".", output_file: str = None):
        """主执行函数"""
        # 如果没有指定输出文件名，使用默认名称
        if output_file is None:
            output_file = "itrace_statistics_summary.xlsx"

        print(f"开始扫描路径: {os.path.abspath(root_path)}")

        # 查找所有itrace.log文件
        itrace_files = self.find_itrace_files(root_path)
        print(f"找到 {len(itrace_files)} 个itrace.log文件")

        if not itrace_files:
            print("未找到任何itrace.log文件")
            return

        results = []
        
        for folder_name, file_path in itrace_files:
            print(f"处理: {folder_name} -> {file_path}")
            
            stall_stats, unit_stats = self.process_itrace_file(file_path)
            
            # 创建结果记录
            result = {'folder_name': folder_name}
            
            # 添加STALL STATISTICS数据 (移除了stall_start_time, stall_end_time)
            if stall_stats:
                for key in ['total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions']:
                    if key in stall_stats:
                        result[key] = stall_stats[key]
                print(f"  - 找到STALL STATISTICS: 断流百分比 {stall_stats['stall_percentage']:.2f}%")
            else:
                print(f"  - 未找到STALL STATISTICS")
                # 添加空值 (移除了stall_start_time, stall_end_time)
                for key in ['total_monitor_time', 'total_stall_time', 'stall_percentage', 'total_tile_instructions']:
                    result[key] = None
            
            # 添加UNIT UTILIZATION STATISTICS数据
            if unit_stats:
                # 只添加利用率数据，不包括cycles数据
                for unit in ['tmac', 'tsfu', 'talu', 'tld', 'tst']:
                    util_key = f'{unit}_utilization'
                    if util_key in unit_stats:
                        result[util_key] = unit_stats[util_key]
                print(f"  - 找到UNIT UTILIZATION STATISTICS")
            else:
                print(f"  - 未找到UNIT UTILIZATION STATISTICS")
                # 添加空值 (移除了total_monitor_cycles和各单元的active/total cycles)
                for key in ['tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization']:
                    result[key] = None
            
            results.append(result)

        # 根据文件扩展名选择输出格式
        if output_file.endswith('.html'):
            self.generate_html_report(results, output_file)
        else:
            # 默认生成Excel报告
            self.generate_excel_report(results, output_file)


def main():
    default_output = 'itrace_statistics_summary.xlsx'

    parser = argparse.ArgumentParser(description='提取itrace.log文件中的统计信息并汇总到Excel文件（按日期分标签页）')
    parser.add_argument('--path', '-p', default='.',
                       help='要扫描的根路径 (默认: 当前目录)')
    parser.add_argument('--output', '-o', default=default_output,
                       help=f'输出Excel文件名 (默认: {default_output})')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出信息')
    parser.add_argument('--report', '-r', action='store_true',
                       help='生成详细的分析报告')

    args = parser.parse_args()

    extractor = ITraceStatsExtractor()
    extractor.verbose = args.verbose
    extractor.run(args.path, args.output)

    if args.report:
        report_file = args.output.replace('.csv', '_report.txt')
        extractor.generate_detailed_report(args.output, report_file)


if __name__ == "__main__":
    main()

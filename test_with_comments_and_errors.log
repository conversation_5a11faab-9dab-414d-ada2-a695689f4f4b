// This is a comment line - should be ignored
// Another comment explaining the data format
// InstrID: Instruction ID
// PC: Program Counter
// Instruction: Raw instruction bytes
// Disassembly: Human readable instruction
// Times: Various execution stage timestamps

InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime

// Valid data rows
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055

// This row has invalid InstrID
invalid_id,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0

// This row has invalid time data
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",invalid_time,1065,0,0,0

// Valid data continues
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125

// Row with missing data
7,0x000000001028,,1130,1135,1140,1145,1165

// Another valid row
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195

// Comment at the end
// End of data file

#!/usr/bin/env python3
"""
测试itrace统计信息提取器
"""

import os
import tempfile
import shutil
from itrace_statistics_extractor import ITraceStatsExtractor


def create_test_data():
    """创建测试数据"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="itrace_test_")
    
    # 测试数据1 - 完整统计信息
    test1_dir = os.path.join(test_dir, "simulation_run_1")
    os.makedirs(test1_dir)
    
    test1_content = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055

// ===== INSTRUCTION STALL STATISTICS =====
// Stall monitor start time (0x9d02 fetch): 1000.00
// Stall monitor end time (thread_done): 5000.00
// Total monitor time: 4000.00
// Total stall time: 800.00
// Stall percentage: 20.00%
// Total tile instructions: 10

// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====
// Total monitor cycles: 4000
// TMAC utilization: 75.50% (3020/4000 cycles)
// TSFU utilization: 45.25% (1810/4000 cycles)
// TALU utilization: 60.75% (2430/4000 cycles)
// TLD utilization: 55.00% (2200/4000 cycles)
// TST utilization: 30.25% (1210/4000 cycles)
"""
    
    with open(os.path.join(test1_dir, "itrace.log"), "w") as f:
        f.write(test1_content)
    
    # 测试数据2 - 只有STALL统计信息
    test2_dir = os.path.join(test_dir, "simulation_run_2")
    os.makedirs(test2_dir)
    
    test2_content = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000002000,0x00000000fc123456,"tld.trii.linear.u64.global t7, (x12)",2000,2005,2010,2015,2025

// ===== INSTRUCTION STALL STATISTICS =====
// Stall monitor start time (0x9d02 fetch): 2000.00
// Stall monitor end time (thread_done): 8000.00
// Total monitor time: 6000.00
// Total stall time: 1200.00
// Stall percentage: 20.00%
// Total tile instructions: 15
"""
    
    with open(os.path.join(test2_dir, "itrace.log"), "w") as f:
        f.write(test2_content)
    
    # 测试数据3 - 没有统计信息
    test3_dir = os.path.join(test_dir, "simulation_run_3")
    os.makedirs(test3_dir)
    
    test3_content = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000003000,0x00000000fd123456,"tmma.nnn t1, t2, t3",3000,3005,3010,3015,3025
2,0x000000003008,0x00000000fd234567,"tst.linear.u32 (x10), t4",3030,3035,3040,3045,3055
"""
    
    with open(os.path.join(test3_dir, "itrace.log"), "w") as f:
        f.write(test3_content)
    
    return test_dir


def test_extractor():
    """测试提取器功能"""
    print("创建测试数据...")
    test_dir = create_test_data()
    
    try:
        print(f"测试目录: {test_dir}")
        
        # 创建提取器实例
        extractor = ITraceStatsExtractor()
        
        # 运行提取器
        output_file = os.path.join(test_dir, "test_results.csv")
        extractor.run(test_dir, output_file)
        
        # 验证结果
        print("\n验证结果:")
        if os.path.exists(output_file):
            print("✓ CSV文件已生成")
            
            with open(output_file, 'r') as f:
                content = f.read()
                print("CSV内容:")
                print(content)
                
            # 简单验证
            lines = content.strip().split('\n')
            if len(lines) >= 4:  # 头部 + 3行数据
                print("✓ 包含预期的数据行数")
            else:
                print("✗ 数据行数不符合预期")
                
            if "simulation_run_1" in content and "simulation_run_2" in content and "simulation_run_3" in content:
                print("✓ 包含所有测试文件夹的数据")
            else:
                print("✗ 缺少某些测试文件夹的数据")
                
        else:
            print("✗ CSV文件未生成")
            
    finally:
        # 清理测试数据
        print(f"\n清理测试目录: {test_dir}")
        shutil.rmtree(test_dir)


if __name__ == "__main__":
    test_extractor()

InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x0000000003f0,0xb3b1799d,"tmma.tnt t1, t17, t15",1000,1004,1008,1016,1024
2,0x0000000003f8,0xcd9c66b3,"tld.linear.u8 t5, (x27)",1039,1042,1045,1048,1059
3,0x000000000400,0x915ef6d1,"tmma.nnn t1, t12, t26",1067,1071,1077,1084,1097
4,0x000000000408,0xd241330b,"tst.shared.u64 (x27), t21",1102,1107,1111,1115,1130
5,0x000000000410,0x27be3111,"ace_bsync x6",1136,1141,1146,1153,1166
6,0x000000000418,0xcacfb3d0,"twait",1171,1178,1181,1187,1194
7,0x000000000420,0x5b0dbb41,"tmma.nnn t23, t12, t4",1207,1210,1218,1222,1236
8,0x000000000428,0xeaf61a26,"tcsrw.i 0x6",1242,1248,1253,1259,1284
9,0x000000000430,0x39a3b2e9,"tmma.ntt t22, t13, t17",1294,1302,1310,1318,1325
10,0x000000000438,0xb28defe3,"tst.shared.u64 (x15), t10",1339,1345,1351,1356,1401
11,0x000000000440,0x4838b326,"tmma.ntt t3, t14, t2",1414,1419,1425,1430,1437
12,0x000000000448,0xf9c349e0,"tmma.nnn t20, t13, t31",1445,1451,1459,1465,1474
13,0x000000000450,0x33bed01d,"tcsrw.i 0x16",1483,1491,1498,1504,1527
14,0x000000000458,0x6cabcc97,"tcsrw.i 0x8",1538,1545,1551,1554,1560
15,0x000000000460,0x37209bdf,"tst.shared.u64 (x27), t4",1566,1572,1578,1585,1604
16,0x000000000468,0x505cacec,"tld.linear.u8 t0, (x7)",1617,1625,1632,1637,1662
17,0x000000000470,0x2c8eaee9,"tld.texture.u16 t27, (x10)",1672,1678,1681,1689,1702
18,0x000000000478,0xd30ff46e,"tst.shared.u64 (x6), t19",1715,1723,1730,1737,1748
19,0x000000000480,0x6fb8d16c,"tst.shared.u64 (x0), t20",1755,1761,1764,1767,1783
20,0x000000000488,0x4d4cbf37,"tld.global.u32 t15, (x5)",1792,1795,1803,1809,1863
21,0x000000000490,0xd40db9b4,"tst.shared.u64 (x8), t30",1876,1883,1887,1892,1913
22,0x000000000498,0x7c52c49f,"tcsrw.i 0x12",1927,1935,1940,1946,1971
23,0x0000000004a0,0x802753a1,"tld.linear.u8 t28, (x7)",1981,1985,1989,1992,2007
24,0x0000000004a8,0xa69b6662,"tld.linear.u8 t14, (x14)",2012,2015,2018,2026,2051
25,0x0000000004b0,0x4a9bedd4,"tmma.tnt t2, t21, t4",2056,2063,2067,2072,2092
26,0x0000000004b8,0x9a0b3c33,"tst.shared.u64 (x30), t15",2100,2106,2112,2116,2124
27,0x0000000004c0,0xb8b317fa,"ace_bsync x22",2130,2136,2142,2148,2154
28,0x0000000004c8,0xb748dbcf,"tmma.tnt t3, t25, t21",2169,2172,2176,2180,2191
29,0x0000000004d0,0x82d8567d,"tst.shared.u64 (x27), t11",2204,2209,2215,2219,2226
30,0x0000000004d8,0xdeda8bbb,"tld.linear.u8 t6, (x3)",2238,2246,2253,2256,2283
31,0x0000000004e0,0x3a935d62,"ace_bsync x31",2291,2297,2301,2307,2313
32,0x0000000004e8,0x710461e3,"tld.global.u32 t24, (x16)",2320,2326,2331,2337,2359
33,0x0000000004f0,0xc7e99aca,"twait",2374,2378,2382,2387,2398
34,0x0000000004f8,0xa44528c0,"tld.linear.u8 t3, (x20)",2403,2406,2409,2416,2436
35,0x000000000500,0xfb5cf467,"tld.linear.u8 t10, (x3)",2449,2456,2459,2463,2470
36,0x000000000508,0x2165e210,"tcsrw.i 0x25",2484,2487,2494,2498,2521
37,0x000000000510,0x1a2c827e,"tmma.nnn t5, t26, t20",2535,2540,2544,2552,2567
38,0x000000000518,0x53ff5011,"ace_bsync x8",2575,2583,2591,2596,2615
39,0x000000000520,0xfdd4253b,"tmma.tnt t0, t29, t6",2625,2628,2635,2639,2660
40,0x000000000528,0x31e8ac68,"tmma.ntt t4, t15, t23",2669,2674,2678,2684,2768
41,0x000000000530,0xac96e9ec,"tld.linear.u8 t0, (x19)",2777,2785,2788,2792,2805
42,0x000000000538,0xf3c43657,"tmma.tnt t9, t17, t18",2811,2818,2822,2830,2845
43,0x000000000540,0xbfffcfd2,"tld.texture.u16 t31, (x16)",2853,2856,2859,2867,2885
44,0x000000000548,0x1b49452d,"tld.global.u32 t21, (x8)",2894,2902,2907,2911,2930
45,0x000000000550,0xc4a69f3c,"ace_bsync x0",2943,2946,2949,2957,2966
46,0x000000000558,0x193923de,"tmma.ntt t9, t27, t8",2979,2982,2987,2992,2998
47,0x000000000560,0x45c7936c,"tcsrw.i 0x6",3008,3013,3020,3026,3050
48,0x000000000568,0xfcfedb99,"tcsrw.i 0x10",3057,3061,3067,3070,3080
49,0x000000000570,0xd84a7b28,"ace_bsync x15",3090,3095,3099,3107,3115
50,0x000000000578,0xef465290,"tld.global.u32 t30, (x14)",3126,3130,3136,3141,3175
51,0x000000000580,0x49118497,"tld.global.u32 t12, (x25)",3183,3188,3193,3196,3209
52,0x000000000588,0xb43825b5,"tld.linear.u8 t25, (x21)",3219,3222,3225,3230,3240
53,0x000000000590,0x53f59a85,"tld.global.u32 t6, (x27)",3254,3259,3267,3272,3290
54,0x000000000598,0x92ec9f2d,"tmma.tnt t24, t12, t16",3304,3307,3315,3321,3326
55,0x0000000005a0,0xfcf27e76,"tld.linear.u8 t12, (x23)",3339,3345,3348,3356,3371
56,0x0000000005a8,0x605cc686,"tmma.tnt t19, t19, t26",3385,3390,3396,3404,3418
57,0x0000000005b0,0x3095eef6,"tcsrw.i 0x26",3431,3439,3445,3453,3463
58,0x0000000005b8,0xa1b0e1d9,"tld.texture.u16 t25, (x0)",3477,3482,3487,3491,3509
59,0x0000000005c0,0xab4e2c24,"tmma.ntt t29, t28, t28",3523,3531,3535,3542,3562
60,0x0000000005c8,0xb8aa7158,"tmma.tnt t18, t21, t5",3569,3573,3581,3586,3650
61,0x0000000005d0,0x35b8fd4b,"tld.global.u32 t2, (x15)",3658,3664,3671,3674,3693
62,0x0000000005d8,0xf2d9de5d,"tmma.nnn t12, t24, t31",3704,3710,3714,3718,3743
63,0x0000000005e0,0xf4855aa1,"tmma.tnt t27, t14, t11",3748,3756,3763,3769,3775
64,0x0000000005e8,0x4fcb7546,"tmma.tnt t29, t8, t29",3788,3796,3803,3810,3834
65,0x0000000005f0,0xd1581092,"twait",3844,3851,3859,3866,3884
66,0x0000000005f8,0x822764e6,"tst.shared.u64 (x30), t28",3897,3902,3906,3914,3927
67,0x000000000600,0x8c0e8cd8,"tcsrw.i 0x17",3940,3946,3949,3957,3971
68,0x000000000608,0x558f1f19,"tmma.ntt t20, t5, t8",3979,3983,3987,3993,4002
69,0x000000000610,0x20714d51,"ace_bsync x26",4010,4015,4022,4028,4046
70,0x000000000618,0x44f3193c,"ace_bsync x24",4051,4058,4066,4069,4118
71,0x000000000620,0x7160a6b4,"twait",4132,4135,4140,4145,4162
72,0x000000000628,0x99c8d2ab,"tld.linear.u8 t14, (x31)",4173,4177,4182,4188,4208
73,0x000000000630,0x738c254c,"tmma.ntt t25, t10, t29",4213,4217,4224,4231,4236
74,0x000000000638,0xa78648f8,"tmma.nnn t1, t5, t27",4247,4251,4257,4261,4267
75,0x000000000640,0x710cf373,"tmma.ntt t13, t29, t20",4276,4281,4287,4292,4310
76,0x000000000648,0xe5bcb8d0,"tmma.tnt t30, t1, t3",4319,4324,4328,4336,4343
77,0x000000000650,0x1a4e5b70,"tld.global.u32 t15, (x12)",4358,4361,4368,4372,4384
78,0x000000000658,0x893b4c32,"tmma.tnt t13, t29, t16",4391,4396,4400,4407,4431
79,0x000000000660,0xd71d5e60,"tst.shared.u64 (x19), t6",4437,4444,4447,4452,4475
80,0x000000000668,0xf87466d7,"ace_bsync x25",4490,4498,4502,4505,4592
81,0x000000000670,0x4e2b6091,"tmma.tnt t19, t7, t2",4607,4612,4619,4625,4641
82,0x000000000678,0x9186a576,"tmma.ntt t0, t26, t31",4647,4650,4656,4661,4686
83,0x000000000680,0xc5122df8,"tst.shared.u64 (x27), t11",4698,4706,4713,4721,4734
84,0x000000000688,0xdee624d0,"tld.linear.u8 t30, (x29)",4748,4754,4762,4769,4782
85,0x000000000690,0xea09dfa0,"tcsrw.i 0x5",4792,4797,4803,4807,4826
86,0x000000000698,0xac3eb2d5,"ace_bsync x21",4840,4843,4849,4854,4864
87,0x0000000006a0,0x464d7c87,"tmma.ntt t16, t21, t17",4876,4883,4891,4896,4918
88,0x0000000006a8,0x9441aefd,"tcsrw.i 0x5",4923,4927,4935,4941,4961
89,0x0000000006b0,0xd2171429,"tcsrw.i 0x30",4974,4982,4990,4996,5015
90,0x0000000006b8,0x27d2582e,"tld.texture.u16 t14, (x25)",5020,5028,5032,5037,5083
91,0x0000000006c0,0x6e781fd7,"twait",5097,5104,5111,5116,5134
92,0x0000000006c8,0x64aebd1b,"tmma.ntt t29, t17, t19",5147,5152,5156,5159,5170
93,0x0000000006d0,0x2e9b23bc,"tld.linear.u8 t11, (x12)",5180,5184,5192,5198,5211
94,0x0000000006d8,0xd29cfc0c,"tld.linear.u8 t18, (x6)",5225,5229,5234,5238,5254
95,0x0000000006e0,0x5d6168bd,"tld.global.u32 t8, (x17)",5261,5264,5267,5274,5288
96,0x0000000006e8,0xb34b6cf6,"twait",5295,5298,5301,5308,5322
97,0x0000000006f0,0x8a8d03aa,"twait",5334,5339,5343,5346,5359
98,0x0000000006f8,0x2d34d08e,"tmma.tnt t25, t31, t4",5371,5378,5386,5394,5400
99,0x000000000700,0x3631d00b,"tmma.nnn t19, t5, t15",5407,5410,5417,5423,5447
100,0x000000000708,0xda6dfda1,"tmma.nnn t14, t24, t28",5461,5467,5472,5479,5556

#!/usr/bin/env python3
"""
ITrace数据分析脚本
用于分析itrace_statistics_extractor.py生成的CSV数据
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import sys
from pathlib import Path


def load_data(csv_file):
    """加载CSV数据"""
    try:
        df = pd.read_csv(csv_file)
        print(f"成功加载数据: {len(df)} 行")
        return df
    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file}")
        return None
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None


def analyze_stall_statistics(df):
    """分析断流统计数据"""
    print("\n=== 断流统计分析 ===")
    
    # 过滤有效的断流数据
    stall_df = df.dropna(subset=['stall_percentage'])
    
    if stall_df.empty:
        print("没有找到有效的断流统计数据")
        return
    
    print(f"有效数据: {len(stall_df)}/{len(df)} 个文件夹")
    
    # 基本统计
    stats = stall_df['stall_percentage'].describe()
    print(f"\n断流百分比统计:")
    print(f"  平均值: {stats['mean']:.2f}%")
    print(f"  中位数: {stats['50%']:.2f}%")
    print(f"  最大值: {stats['max']:.2f}%")
    print(f"  最小值: {stats['min']:.2f}%")
    print(f"  标准差: {stats['std']:.2f}%")
    
    # 找出异常值
    q75 = stats['75%']
    q25 = stats['25%']
    iqr = q75 - q25
    upper_bound = q75 + 1.5 * iqr
    lower_bound = q25 - 1.5 * iqr
    
    outliers = stall_df[(stall_df['stall_percentage'] > upper_bound) | 
                       (stall_df['stall_percentage'] < lower_bound)]
    
    if not outliers.empty:
        print(f"\n异常值检测 (IQR方法):")
        for _, row in outliers.iterrows():
            print(f"  {row['folder_name']}: {row['stall_percentage']:.2f}%")
    
    # 排序显示
    print(f"\n断流百分比排序 (前5名):")
    top5 = stall_df.nlargest(5, 'stall_percentage')
    for _, row in top5.iterrows():
        print(f"  {row['folder_name']}: {row['stall_percentage']:.2f}%")


def analyze_unit_utilization(df):
    """分析功能单元利用率数据"""
    print("\n=== 功能单元利用率分析 ===")
    
    # 过滤有效的利用率数据
    unit_df = df.dropna(subset=['total_monitor_cycles'])
    
    if unit_df.empty:
        print("没有找到有效的功能单元利用率数据")
        return
    
    print(f"有效数据: {len(unit_df)}/{len(df)} 个文件夹")
    
    units = ['tmac', 'tsfu', 'talu', 'tld', 'tst']
    
    print(f"\n各功能单元平均利用率:")
    for unit in units:
        util_col = f'{unit}_utilization'
        if util_col in unit_df.columns:
            avg_util = unit_df[util_col].mean()
            max_util = unit_df[util_col].max()
            min_util = unit_df[util_col].min()
            print(f"  {unit.upper()}: 平均 {avg_util:.2f}%, 最大 {max_util:.2f}%, 最小 {min_util:.2f}%")
    
    # 找出利用率最高和最低的文件夹
    print(f"\n各功能单元利用率最高的文件夹:")
    for unit in units:
        util_col = f'{unit}_utilization'
        if util_col in unit_df.columns:
            max_row = unit_df.loc[unit_df[util_col].idxmax()]
            print(f"  {unit.upper()}: {max_row['folder_name']} ({max_row[util_col]:.2f}%)")


def create_visualizations(df, output_dir="plots"):
    """创建可视化图表"""
    print(f"\n=== 生成可视化图表 ===")
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    # 设置图表样式
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # 1. 断流百分比分布直方图
    stall_df = df.dropna(subset=['stall_percentage'])
    if not stall_df.empty:
        plt.figure(figsize=(10, 6))
        plt.hist(stall_df['stall_percentage'], bins=20, alpha=0.7, edgecolor='black')
        plt.xlabel('Stall Percentage (%)')
        plt.ylabel('Frequency')
        plt.title('Distribution of Stall Percentages')
        plt.grid(True, alpha=0.3)
        plt.savefig(f'{output_dir}/stall_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"  生成: {output_dir}/stall_distribution.png")
    
    # 2. 功能单元利用率箱线图
    unit_df = df.dropna(subset=['total_monitor_cycles'])
    if not unit_df.empty:
        units = ['tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization']
        unit_data = unit_df[units]
        
        plt.figure(figsize=(12, 8))
        unit_data.boxplot()
        plt.ylabel('Utilization (%)')
        plt.title('Functional Unit Utilization Comparison')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        plt.savefig(f'{output_dir}/unit_utilization_boxplot.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"  生成: {output_dir}/unit_utilization_boxplot.png")
    
    # 3. 断流百分比 vs 功能单元利用率散点图
    if not stall_df.empty and not unit_df.empty:
        merged_df = pd.merge(stall_df, unit_df, on='folder_name', how='inner')
        if not merged_df.empty:
            fig, axes = plt.subplots(2, 3, figsize=(15, 10))
            axes = axes.flatten()
            
            units = ['tmac_utilization', 'tsfu_utilization', 'talu_utilization', 'tld_utilization', 'tst_utilization']
            
            for i, unit in enumerate(units):
                if unit in merged_df.columns:
                    axes[i].scatter(merged_df['stall_percentage'], merged_df[unit], alpha=0.7)
                    axes[i].set_xlabel('Stall Percentage (%)')
                    axes[i].set_ylabel(f'{unit.split("_")[0].upper()} Utilization (%)')
                    axes[i].set_title(f'Stall vs {unit.split("_")[0].upper()} Utilization')
                    axes[i].grid(True, alpha=0.3)
            
            # 隐藏多余的子图
            if len(units) < len(axes):
                axes[-1].set_visible(False)
            
            plt.tight_layout()
            plt.savefig(f'{output_dir}/stall_vs_utilization.png', dpi=300, bbox_inches='tight')
            plt.close()
            print(f"  生成: {output_dir}/stall_vs_utilization.png")


def main():
    parser = argparse.ArgumentParser(description='分析ITrace统计数据')
    parser.add_argument('csv_file', help='CSV数据文件路径')
    parser.add_argument('--plot', '-p', action='store_true', help='生成可视化图表')
    parser.add_argument('--output-dir', '-o', default='plots', help='图表输出目录')
    
    args = parser.parse_args()
    
    # 检查依赖
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
    except ImportError:
        print("错误: 需要安装matplotlib和seaborn库")
        print("请运行: pip install matplotlib seaborn")
        sys.exit(1)
    
    # 加载数据
    df = load_data(args.csv_file)
    if df is None:
        sys.exit(1)
    
    # 执行分析
    analyze_stall_statistics(df)
    analyze_unit_utilization(df)
    
    # 生成图表
    if args.plot:
        create_visualizations(df, args.output_dir)
    
    print(f"\n分析完成!")


if __name__ == "__main__":
    main()

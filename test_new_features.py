#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增功能的脚本
验证缩放上限20x和WASD+R快捷键功能
"""

import os
import sys
import subprocess
import webbrowser
from pathlib import Path

def test_html_generation():
    """测试HTML文件生成"""
    print("🔧 测试HTML文件生成...")
    
    # 运行可视化器
    result = subprocess.run([
        sys.executable, 'itrace_timeline_visualizer.py', 
        'itrace.log', '-o', 'test_features.html'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ HTML文件生成成功")
        return True
    else:
        print(f"❌ HTML文件生成失败: {result.stderr}")
        return False

def check_html_features():
    """检查HTML文件中的新功能"""
    print("🔍 检查HTML文件中的新功能...")
    
    html_file = 'test_features.html'
    if not os.path.exists(html_file):
        print("❌ HTML文件不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查缩放上限
    if 'max="20"' in content:
        print("✅ 缩放上限已设置为20x")
    else:
        print("❌ 缩放上限未正确设置")
        return False
    
    # 检查快捷键说明
    if 'R: 复位缩放' in content:
        print("✅ R键复位缩放功能已添加")
    else:
        print("❌ R键复位缩放功能未添加")
        return False
    
    # 检查WASD快捷键
    if "case 'w':" in content and "case 's':" in content and "case 'a':" in content and "case 'd':" in content:
        print("✅ WASD快捷键已实现")
    else:
        print("❌ WASD快捷键未正确实现")
        return False
    
    # 检查R键功能
    if "case 'r':" in content:
        print("✅ R键功能已实现")
    else:
        print("❌ R键功能未实现")
        return False
    
    # 检查背景扩展修复
    if 'row.style.minWidth = (800 * currentZoom)' in content:
        print("✅ 背景扩展修复已应用")
    else:
        print("❌ 背景扩展修复未应用")
        return False
    
    return True

def open_test_file():
    """打开测试文件供手动验证"""
    html_file = 'test_features.html'
    if os.path.exists(html_file):
        file_path = os.path.abspath(html_file)
        print(f"🌐 打开测试文件: {file_path}")
        webbrowser.open(f'file://{file_path}')
        
        print("\n📋 手动测试清单:")
        print("1. 拖动缩放滑块，确认可以缩放到20x")
        print("2. 按W键测试放大功能")
        print("3. 按S键测试缩小功能") 
        print("4. 按A键测试左移功能")
        print("5. 按D键测试右移功能")
        print("6. 按R键测试复位缩放功能")
        print("7. 放大后右移，确认背景正确扩展到右侧")
        print("8. 确认在输入框中输入时快捷键不会干扰")
        
        return True
    else:
        print("❌ 测试文件不存在")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("ITrace Timeline Visualizer 新功能测试")
    print("=" * 60)
    
    # 检查必要文件
    if not os.path.exists('itrace_timeline_visualizer.py'):
        print("❌ 找不到 itrace_timeline_visualizer.py")
        return False
    
    if not os.path.exists('itrace.log'):
        print("❌ 找不到 itrace.log，请先运行可视化器生成测试数据")
        return False
    
    # 运行测试
    success = True
    
    # 测试HTML生成
    if not test_html_generation():
        success = False
    
    # 检查功能
    if not check_html_features():
        success = False
    
    if success:
        print("\n🎉 所有自动测试通过!")
        print("-" * 60)
        
        # 打开文件进行手动测试
        if open_test_file():
            print("\n💡 请在浏览器中手动测试上述功能")
        
    else:
        print("\n❌ 部分测试失败")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

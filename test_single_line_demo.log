// ITrace Timeline Visualizer Demo Data
// This file demonstrates the single-line timeline display
// Four execution stages: Fetch, Decode, Dispatch, Execute

InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime

// Sequential instructions with overlapping stages
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1003,1008,1013,1018,1030
3,0x000000001010,0x00000000fb345678,"tcsrw.i 0x5",1006,1011,1016,1025,1035
4,0x000000001018,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1009,1014,1020,1030,1040
5,0x000000001020,0x00000000fb567890,"tmma.tnt t4, t2, t3",1012,1017,1025,1035,1050

// Instructions with different execution patterns
6,0x000000001028,0x00000000fb678901,"tst.linear.u32 (x12), t7",1015,1020,1030,1040,1045
7,0x000000001030,0x00000000fb789012,"ace_bsync x0",1018,1023,1035,1045,1050
8,0x000000001038,0x00000000fb890123,"twait",1021,1026,1040,1050,1055
9,0x000000001040,0x00000000fb901234,"tld.trii.linear.u64.global t8, (x13)",1024,1029,1045,1055,1065
10,0x000000001048,0x00000000fba01345,"tmma.ttn t5, t6, t7",1027,1032,1050,1060,1075

// Long-running instructions
11,0x000000001050,0x00000000fbb01456,"complex_operation t9, t10",1030,1035,1055,1065,1090
12,0x000000001058,0x00000000fbc01567,"vector_multiply t11, t12, t13",1033,1038,1060,1070,1100
13,0x000000001060,0x00000000fbd01678,"matrix_transform t14, t15",1036,1041,1065,1075,1110
14,0x000000001068,0x00000000fbe01789,"parallel_reduce t16, t17",1039,1044,1070,1080,1120
15,0x000000001070,0x00000000fbf0189a,"sync_barrier",1042,1047,1075,1085,1125


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace统计信息报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .summary {
            padding: 30px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .summary h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #495057;
            font-size: 1.1em;
        }
        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .data-section {
            padding: 30px;
        }
        .data-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        th {
            background-color: #667eea;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .percentage {
            font-weight: bold;
            color: #28a745;
        }
        .folder-name {
            font-weight: bold;
            color: #495057;
        }
        .no-data {
            color: #6c757d;
            font-style: italic;
        }
        .footer {
            padding: 20px 30px;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }
        .chart-container {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ITrace统计信息报告</h1>
            <p>生成时间: 2025年08月12日 14:17:27</p>
        </div>

        <div class="summary">
            <h2>统计摘要</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>总文件夹数</h3>
                    <div class="value">2</div>
                </div>
                <div class="stat-card">
                    <h3>包含STALL统计</h3>
                    <div class="value">2</div>
                </div>
                <div class="stat-card">
                    <h3>包含UNIT统计</h3>
                    <div class="value">2</div>
                </div>
                <div class="stat-card">
                    <h3>平均断流百分比</h3>
                    <div class="value">20.00%</div>
                </div>
            </div>
        </div>

        <div class="data-section">
            <h2>详细数据</h2>
            <table>
                <thead>
                    <tr>
                        <th>Test Case</th>
                        <th>总监控时间</th>
                        <th>总断流时间</th>
                        <th>断流百分比</th>
                        <th>Tile指令数</th>
                        <th>TMAC利用率</th>
                        <th>TSFU利用率</th>
                        <th>TALU利用率</th>
                        <th>TLD利用率</th>
                        <th>TST利用率</th>
                    </tr>
                </thead>
                <tbody>
                    
                    <tr>
                        <td class="folder-name">test_folder</td>
                        <td>4000.0</td>
                        <td>800.0</td>
                        <td class="percentage">20.00%</td>
                        <td>10</td>
                        <td class="percentage">75.50%</td>
                        <td class="percentage">45.25%</td>
                        <td class="percentage">60.75%</td>
                        <td class="percentage">55.00%</td>
                        <td class="percentage">30.25%</td>
                    </tr>
                    <tr>
                        <td class="folder-name">test_folder2</td>
                        <td>6000.0</td>
                        <td>1200.0</td>
                        <td class="percentage">20.00%</td>
                        <td>15</td>
                        <td class="percentage">80.25%</td>
                        <td class="percentage">50.75%</td>
                        <td class="percentage">65.50%</td>
                        <td class="percentage">70.00%</td>
                        <td class="percentage">35.25%</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>由 ITrace统计信息提取器 生成 | 扫描路径: .</p>
        </div>
    </div>
</body>
</html>

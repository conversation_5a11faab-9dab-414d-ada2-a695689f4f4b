# 单行时间线显示改进

## 概述

根据用户需求，将ITrace Timeline Visualizer的四个执行阶段（Fetch、Decode、Dispatch、Execute）从原来的4行分层显示改为单行显示，提高了时间线的紧凑性和可读性。

## 主要改进

### 1. 布局优化
- **原来**: 四个阶段分别显示在不同的垂直位置（top: 5px, 20px, 35px, 50px）
- **现在**: 所有阶段都在同一行显示，通过轻微的垂直偏移来避免完全重叠

### 2. 视觉设计改进

#### 阶段条样式
- **高度**: 从12px增加到18px，提高可见性
- **边框**: 添加了1px的深色边框，增强边界定义
- **阴影**: 添加了轻微的阴影效果，增加立体感
- **字体**: 调整为8px，确保在较小的条形中仍然可读

#### 层次结构
```css
.stage-bar.fetch    { z-index: 4; top: 2px; }  // 最上层
.stage-bar.decode   { z-index: 3; top: 4px; }  // 第二层
.stage-bar.dispatch { z-index: 2; top: 6px; }  // 第三层
.stage-bar.execute  { z-index: 1; top: 8px; }  // 最下层
```

#### 交互效果
- **悬停效果**: 放大1.05倍，z-index提升到100，增强边框
- **阴影增强**: 悬停时阴影更明显，提供更好的视觉反馈

### 3. 容器调整
- **时间线容器高度**: 从60px减少到30px
- **指令行高度**: 固定为45px，确保一致性
- **间距优化**: 减少行间距从15px到8px，提高密度

### 4. 颜色方案保持不变
- **Fetch**: #FF6B6B (红色)
- **Decode**: #4ECDC4 (青色)
- **Dispatch**: #45B7D1 (蓝色)
- **Execute**: #96CEB4 (绿色)

## 技术实现

### CSS关键改进
```css
.timeline-bars {
    height: 30px;           // 减少高度
    position: relative;
}

.stage-bar {
    height: 18px;           // 增加条形高度
    border-radius: 3px;     // 调整圆角
    border: 1px solid rgba(0,0,0,0.2);  // 添加边框
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);  // 添加阴影
    font-size: 8px;         // 调整字体大小
}

.stage-bar:hover {
    transform: scale(1.05); // 悬停放大效果
    z-index: 100 !important; // 确保悬停时在最上层
    box-shadow: 0 3px 6px rgba(0,0,0,0.4); // 增强阴影
}
```

### 垂直偏移策略
通过为每个阶段设置不同的`top`值（2px, 4px, 6px, 8px），创建了轻微的层叠效果：
- 避免了完全重叠导致的视觉混乱
- 保持了紧凑的单行布局
- 在重叠区域仍能区分不同阶段

## 使用效果

### 优势
1. **空间效率**: 每个指令只占用一行，显示更多指令
2. **时间关系清晰**: 阶段重叠更容易观察并行执行
3. **视觉简洁**: 减少了垂直空间的使用
4. **交互友好**: 悬停效果帮助识别特定阶段

### 适用场景
- 需要查看大量指令的执行时间线
- 分析指令流水线的并行度
- 识别执行瓶颈和重叠模式
- 比较不同指令的执行特征

## 示例数据

创建了`test_single_line_demo.log`文件，包含15条指令，展示了：
- 顺序指令的重叠执行
- 不同执行模式的指令
- 长时间运行的复杂操作
- 同步和并行操作

## 兼容性

- 保持了所有原有功能（缩放、过滤、排序）
- 工具提示仍然正常工作
- 键盘快捷键功能不变
- 响应式设计保持一致

## 总结

单行时间线显示显著提高了ITrace Timeline Visualizer的空间利用率和可读性，特别适合分析包含大量指令的执行轨迹。通过巧妙的视觉设计和层次安排，在保持清晰度的同时实现了紧凑的布局。


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }

        .timeline-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
            position: relative;
        }

        .instructions-wrapper {
            overflow-x: auto;
            overflow-y: visible;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-left: auto;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .time-axis {
            position: relative;
            height: 40px;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            min-width: 800px;
            overflow-x: auto;
        }

        .time-tick {
            position: absolute;
            bottom: 0;
            width: 1px;
            height: 10px;
            background: #666;
        }

        .time-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: translateX(-50%);
        }

        .instruction-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px 8px 8px 0px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
            min-width: 800px;
            height: 45px;
            position: relative;
        }

        .instruction-row:hover {
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .instruction-info {
            width: 350px;
            flex-shrink: 0;
            padding-right: 20px;
            position: sticky;
            left: 0;
            background: #fafafa;
            z-index: 10;
            border-right: 2px solid #e0e0e0;
        }

        .instruction-pc {
            color: #666;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .instruction-disasm {
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            font-size: 1.0em;
            font-weight: bold;
            word-break: break-all;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }

        .timeline-bars {
            flex: 1;
            position: relative;
            height: 30px;
            min-width: 800px;
        }

        .stage-bar {
            position: absolute;
            height: 18px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.7);
            cursor: pointer;
            transition: all 0.2s ease;
            top: 3px;
            border: 1px solid rgba(0,0,0,0.2);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .stage-bar:hover {
            transform: scale(1.05);
            z-index: 100 !important;
            box-shadow: 0 3px 6px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.8);
        }

        /* 所有阶段都在同一高度 */
        .stage-bar.fetch {
            z-index: 4;
            top: 6px;
        }
        .stage-bar.decode {
            z-index: 3;
            top: 6px;
        }
        .stage-bar.dispatch {
            z-index: 2;
            top: 6px;
        }
        .stage-bar.execute {
            z-index: 1;
            top: 6px;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            color: #555;
        }

        .control-group input, .control-group select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .summary {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .summary h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .summary-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .summary-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>15</strong></div>
            <div>时间范围: <strong>1000 - 1125</strong></div>
            <div>总执行时间: <strong>125</strong></div>
            <div>生成时间: <strong>2025-08-07 15:31:00</strong></div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline-header">
            <h3 style="margin: 0;">指令执行时间线</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>Fetch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ECDC4;"></div>
                    <span>Decode</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>Dispatch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96CEB4;"></div>
                    <span>Execute</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>缩放:</label>
                <input type="range" id="zoomSlider" min="0.1" max="20" step="0.1" value="1">
                <span id="zoomValue">1.0x</span>
            </div>
            <div class="control-group">
                <label>过滤指令:</label>
                <input type="text" id="filterInput" placeholder="输入PC地址或反汇编代码...">
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortSelect">
                    <option value="id">指令ID</option>
                    <option value="start_time">开始时间</option>
                    <option value="duration">执行时长</option>
                </select>
            </div>
            <button class="btn" onclick="resetView()">重置视图</button>
            <div class="control-group">
                <label>快捷键:</label>
                <span style="font-size: 12px; color: #666;">W/S: 放大/缩小 | A/D: 左移/右移 | R: 复位缩放</span>
            </div>
        </div>

        <div class="time-axis" id="timeAxis"></div>

        <div class="instructions-wrapper">
            <div id="instructionList">
                
                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001000</div>
                        <div class="instruction-disasm">tld.trii.linear.u32.global t5, (x10)</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 0.00%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 4.00%; width: 4.00%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 8.00%; width: 4.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 12.00%; width: 4.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}, {"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001008</div>
                        <div class="instruction-disasm">tmma.ttt t3, t1, t2</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 2.40%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1003, "end": 1008, "duration": 5, "color": "#FF6B6B"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1003, "end": 1008, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1008, "end": 1013, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1013, "end": 1018, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1018, "end": 1030, "duration": 12, "color": "#96CEB4"}], "total_start": 1003, "total_end": 1030, "total_duration": 27})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 6.40%; width: 4.00%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1008, "end": 1013, "duration": 5, "color": "#4ECDC4"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1003, "end": 1008, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1008, "end": 1013, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1013, "end": 1018, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1018, "end": 1030, "duration": 12, "color": "#96CEB4"}], "total_start": 1003, "total_end": 1030, "total_duration": 27})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 10.40%; width: 4.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1013, "end": 1018, "duration": 5, "color": "#45B7D1"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1003, "end": 1008, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1008, "end": 1013, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1013, "end": 1018, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1018, "end": 1030, "duration": 12, "color": "#96CEB4"}], "total_start": 1003, "total_end": 1030, "total_duration": 27})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 14.40%; width: 9.60%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1018, "end": 1030, "duration": 12, "color": "#96CEB4"}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1003, "end": 1008, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1008, "end": 1013, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1013, "end": 1018, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1018, "end": 1030, "duration": 12, "color": "#96CEB4"}], "total_start": 1003, "total_end": 1030, "total_duration": 27})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001010</div>
                        <div class="instruction-disasm">tcsrw.i 0x5</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 4.80%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1006, "end": 1011, "duration": 5, "color": "#FF6B6B"}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1006, "end": 1011, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1011, "end": 1016, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1016, "end": 1025, "duration": 9, "color": "#45B7D1"}, {"name": "execute", "start": 1025, "end": 1035, "duration": 10, "color": "#96CEB4"}], "total_start": 1006, "total_end": 1035, "total_duration": 29})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 8.80%; width: 4.00%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1011, "end": 1016, "duration": 5, "color": "#4ECDC4"}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1006, "end": 1011, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1011, "end": 1016, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1016, "end": 1025, "duration": 9, "color": "#45B7D1"}, {"name": "execute", "start": 1025, "end": 1035, "duration": 10, "color": "#96CEB4"}], "total_start": 1006, "total_end": 1035, "total_duration": 29})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 12.80%; width: 7.20%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1016, "end": 1025, "duration": 9, "color": "#45B7D1"}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1006, "end": 1011, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1011, "end": 1016, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1016, "end": 1025, "duration": 9, "color": "#45B7D1"}, {"name": "execute", "start": 1025, "end": 1035, "duration": 10, "color": "#96CEB4"}], "total_start": 1006, "total_end": 1035, "total_duration": 29})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 20.00%; width: 8.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1025, "end": 1035, "duration": 10, "color": "#96CEB4"}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1006, "end": 1011, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1011, "end": 1016, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1016, "end": 1025, "duration": 9, "color": "#45B7D1"}, {"name": "execute", "start": 1025, "end": 1035, "duration": 10, "color": "#96CEB4"}], "total_start": 1006, "total_end": 1035, "total_duration": 29})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001018</div>
                        <div class="instruction-disasm">tld.linear.u16 t6, (x11)</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 7.20%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1009, "end": 1014, "duration": 5, "color": "#FF6B6B"}, {"id": 4, "pc": "0x000000001018", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1009, "end": 1014, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1014, "end": 1020, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1020, "end": 1030, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1030, "end": 1040, "duration": 10, "color": "#96CEB4"}], "total_start": 1009, "total_end": 1040, "total_duration": 31})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 11.20%; width: 4.80%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1014, "end": 1020, "duration": 6, "color": "#4ECDC4"}, {"id": 4, "pc": "0x000000001018", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1009, "end": 1014, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1014, "end": 1020, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1020, "end": 1030, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1030, "end": 1040, "duration": 10, "color": "#96CEB4"}], "total_start": 1009, "total_end": 1040, "total_duration": 31})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 16.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1020, "end": 1030, "duration": 10, "color": "#45B7D1"}, {"id": 4, "pc": "0x000000001018", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1009, "end": 1014, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1014, "end": 1020, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1020, "end": 1030, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1030, "end": 1040, "duration": 10, "color": "#96CEB4"}], "total_start": 1009, "total_end": 1040, "total_duration": 31})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 24.00%; width: 8.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1030, "end": 1040, "duration": 10, "color": "#96CEB4"}, {"id": 4, "pc": "0x000000001018", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1009, "end": 1014, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1014, "end": 1020, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1020, "end": 1030, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1030, "end": 1040, "duration": 10, "color": "#96CEB4"}], "total_start": 1009, "total_end": 1040, "total_duration": 31})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001020</div>
                        <div class="instruction-disasm">tmma.tnt t4, t2, t3</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 9.60%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1012, "end": 1017, "duration": 5, "color": "#FF6B6B"}, {"id": 5, "pc": "0x000000001020", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1012, "end": 1017, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1017, "end": 1025, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1025, "end": 1035, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1035, "end": 1050, "duration": 15, "color": "#96CEB4"}], "total_start": 1012, "total_end": 1050, "total_duration": 38})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 13.60%; width: 6.40%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1017, "end": 1025, "duration": 8, "color": "#4ECDC4"}, {"id": 5, "pc": "0x000000001020", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1012, "end": 1017, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1017, "end": 1025, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1025, "end": 1035, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1035, "end": 1050, "duration": 15, "color": "#96CEB4"}], "total_start": 1012, "total_end": 1050, "total_duration": 38})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 20.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1025, "end": 1035, "duration": 10, "color": "#45B7D1"}, {"id": 5, "pc": "0x000000001020", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1012, "end": 1017, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1017, "end": 1025, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1025, "end": 1035, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1035, "end": 1050, "duration": 15, "color": "#96CEB4"}], "total_start": 1012, "total_end": 1050, "total_duration": 38})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 28.00%; width: 12.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1035, "end": 1050, "duration": 15, "color": "#96CEB4"}, {"id": 5, "pc": "0x000000001020", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1012, "end": 1017, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1017, "end": 1025, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1025, "end": 1035, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1035, "end": 1050, "duration": 15, "color": "#96CEB4"}], "total_start": 1012, "total_end": 1050, "total_duration": 38})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001028</div>
                        <div class="instruction-disasm">tst.linear.u32 (x12), t7</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 12.00%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1015, "end": 1020, "duration": 5, "color": "#FF6B6B"}, {"id": 6, "pc": "0x000000001028", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1015, "end": 1020, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1020, "end": 1030, "duration": 10, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1030, "end": 1040, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1045, "duration": 5, "color": "#96CEB4"}], "total_start": 1015, "total_end": 1045, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 16.00%; width: 8.00%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1020, "end": 1030, "duration": 10, "color": "#4ECDC4"}, {"id": 6, "pc": "0x000000001028", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1015, "end": 1020, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1020, "end": 1030, "duration": 10, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1030, "end": 1040, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1045, "duration": 5, "color": "#96CEB4"}], "total_start": 1015, "total_end": 1045, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 24.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1030, "end": 1040, "duration": 10, "color": "#45B7D1"}, {"id": 6, "pc": "0x000000001028", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1015, "end": 1020, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1020, "end": 1030, "duration": 10, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1030, "end": 1040, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1045, "duration": 5, "color": "#96CEB4"}], "total_start": 1015, "total_end": 1045, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 32.00%; width: 4.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1040, "end": 1045, "duration": 5, "color": "#96CEB4"}, {"id": 6, "pc": "0x000000001028", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1015, "end": 1020, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1020, "end": 1030, "duration": 10, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1030, "end": 1040, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1045, "duration": 5, "color": "#96CEB4"}], "total_start": 1015, "total_end": 1045, "total_duration": 30})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001030</div>
                        <div class="instruction-disasm">ace_bsync x0</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 14.40%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1018, "end": 1023, "duration": 5, "color": "#FF6B6B"}, {"id": 7, "pc": "0x000000001030", "instruction": "0x00000000fb789012", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1018, "end": 1023, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1023, "end": 1035, "duration": 12, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1045, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1045, "end": 1050, "duration": 5, "color": "#96CEB4"}], "total_start": 1018, "total_end": 1050, "total_duration": 32})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 18.40%; width: 9.60%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1023, "end": 1035, "duration": 12, "color": "#4ECDC4"}, {"id": 7, "pc": "0x000000001030", "instruction": "0x00000000fb789012", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1018, "end": 1023, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1023, "end": 1035, "duration": 12, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1045, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1045, "end": 1050, "duration": 5, "color": "#96CEB4"}], "total_start": 1018, "total_end": 1050, "total_duration": 32})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 28.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1035, "end": 1045, "duration": 10, "color": "#45B7D1"}, {"id": 7, "pc": "0x000000001030", "instruction": "0x00000000fb789012", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1018, "end": 1023, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1023, "end": 1035, "duration": 12, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1045, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1045, "end": 1050, "duration": 5, "color": "#96CEB4"}], "total_start": 1018, "total_end": 1050, "total_duration": 32})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 36.00%; width: 4.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1045, "end": 1050, "duration": 5, "color": "#96CEB4"}, {"id": 7, "pc": "0x000000001030", "instruction": "0x00000000fb789012", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1018, "end": 1023, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1023, "end": 1035, "duration": 12, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1045, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1045, "end": 1050, "duration": 5, "color": "#96CEB4"}], "total_start": 1018, "total_end": 1050, "total_duration": 32})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001038</div>
                        <div class="instruction-disasm">twait</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 16.80%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1021, "end": 1026, "duration": 5, "color": "#FF6B6B"}, {"id": 8, "pc": "0x000000001038", "instruction": "0x00000000fb890123", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1021, "end": 1026, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1026, "end": 1040, "duration": 14, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1040, "end": 1050, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1050, "end": 1055, "duration": 5, "color": "#96CEB4"}], "total_start": 1021, "total_end": 1055, "total_duration": 34})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 20.80%; width: 11.20%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1026, "end": 1040, "duration": 14, "color": "#4ECDC4"}, {"id": 8, "pc": "0x000000001038", "instruction": "0x00000000fb890123", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1021, "end": 1026, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1026, "end": 1040, "duration": 14, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1040, "end": 1050, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1050, "end": 1055, "duration": 5, "color": "#96CEB4"}], "total_start": 1021, "total_end": 1055, "total_duration": 34})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 32.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1040, "end": 1050, "duration": 10, "color": "#45B7D1"}, {"id": 8, "pc": "0x000000001038", "instruction": "0x00000000fb890123", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1021, "end": 1026, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1026, "end": 1040, "duration": 14, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1040, "end": 1050, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1050, "end": 1055, "duration": 5, "color": "#96CEB4"}], "total_start": 1021, "total_end": 1055, "total_duration": 34})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 40.00%; width: 4.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1050, "end": 1055, "duration": 5, "color": "#96CEB4"}, {"id": 8, "pc": "0x000000001038", "instruction": "0x00000000fb890123", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1021, "end": 1026, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1026, "end": 1040, "duration": 14, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1040, "end": 1050, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1050, "end": 1055, "duration": 5, "color": "#96CEB4"}], "total_start": 1021, "total_end": 1055, "total_duration": 34})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001040</div>
                        <div class="instruction-disasm">tld.trii.linear.u64.global t8, (x13)</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 19.20%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1024, "end": 1029, "duration": 5, "color": "#FF6B6B"}, {"id": 9, "pc": "0x000000001040", "instruction": "0x00000000fb901234", "disassembly": "tld.trii.linear.u64.global t8, (x13)", "stages": [{"name": "fetch", "start": 1024, "end": 1029, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1029, "end": 1045, "duration": 16, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1045, "end": 1055, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1055, "end": 1065, "duration": 10, "color": "#96CEB4"}], "total_start": 1024, "total_end": 1065, "total_duration": 41})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 23.20%; width: 12.80%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1029, "end": 1045, "duration": 16, "color": "#4ECDC4"}, {"id": 9, "pc": "0x000000001040", "instruction": "0x00000000fb901234", "disassembly": "tld.trii.linear.u64.global t8, (x13)", "stages": [{"name": "fetch", "start": 1024, "end": 1029, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1029, "end": 1045, "duration": 16, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1045, "end": 1055, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1055, "end": 1065, "duration": 10, "color": "#96CEB4"}], "total_start": 1024, "total_end": 1065, "total_duration": 41})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 36.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1045, "end": 1055, "duration": 10, "color": "#45B7D1"}, {"id": 9, "pc": "0x000000001040", "instruction": "0x00000000fb901234", "disassembly": "tld.trii.linear.u64.global t8, (x13)", "stages": [{"name": "fetch", "start": 1024, "end": 1029, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1029, "end": 1045, "duration": 16, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1045, "end": 1055, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1055, "end": 1065, "duration": 10, "color": "#96CEB4"}], "total_start": 1024, "total_end": 1065, "total_duration": 41})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 44.00%; width: 8.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1055, "end": 1065, "duration": 10, "color": "#96CEB4"}, {"id": 9, "pc": "0x000000001040", "instruction": "0x00000000fb901234", "disassembly": "tld.trii.linear.u64.global t8, (x13)", "stages": [{"name": "fetch", "start": 1024, "end": 1029, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1029, "end": 1045, "duration": 16, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1045, "end": 1055, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1055, "end": 1065, "duration": 10, "color": "#96CEB4"}], "total_start": 1024, "total_end": 1065, "total_duration": 41})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001048</div>
                        <div class="instruction-disasm">tmma.ttn t5, t6, t7</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 21.60%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1027, "end": 1032, "duration": 5, "color": "#FF6B6B"}, {"id": 10, "pc": "0x000000001048", "instruction": "0x00000000fba01345", "disassembly": "tmma.ttn t5, t6, t7", "stages": [{"name": "fetch", "start": 1027, "end": 1032, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1032, "end": 1050, "duration": 18, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1050, "end": 1060, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1060, "end": 1075, "duration": 15, "color": "#96CEB4"}], "total_start": 1027, "total_end": 1075, "total_duration": 48})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 25.60%; width: 14.40%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1032, "end": 1050, "duration": 18, "color": "#4ECDC4"}, {"id": 10, "pc": "0x000000001048", "instruction": "0x00000000fba01345", "disassembly": "tmma.ttn t5, t6, t7", "stages": [{"name": "fetch", "start": 1027, "end": 1032, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1032, "end": 1050, "duration": 18, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1050, "end": 1060, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1060, "end": 1075, "duration": 15, "color": "#96CEB4"}], "total_start": 1027, "total_end": 1075, "total_duration": 48})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 40.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1050, "end": 1060, "duration": 10, "color": "#45B7D1"}, {"id": 10, "pc": "0x000000001048", "instruction": "0x00000000fba01345", "disassembly": "tmma.ttn t5, t6, t7", "stages": [{"name": "fetch", "start": 1027, "end": 1032, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1032, "end": 1050, "duration": 18, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1050, "end": 1060, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1060, "end": 1075, "duration": 15, "color": "#96CEB4"}], "total_start": 1027, "total_end": 1075, "total_duration": 48})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 48.00%; width: 12.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1060, "end": 1075, "duration": 15, "color": "#96CEB4"}, {"id": 10, "pc": "0x000000001048", "instruction": "0x00000000fba01345", "disassembly": "tmma.ttn t5, t6, t7", "stages": [{"name": "fetch", "start": 1027, "end": 1032, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1032, "end": 1050, "duration": 18, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1050, "end": 1060, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1060, "end": 1075, "duration": 15, "color": "#96CEB4"}], "total_start": 1027, "total_end": 1075, "total_duration": 48})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001050</div>
                        <div class="instruction-disasm">complex_operation t9, t10</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 24.00%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1030, "end": 1035, "duration": 5, "color": "#FF6B6B"}, {"id": 11, "pc": "0x000000001050", "instruction": "0x00000000fbb01456", "disassembly": "complex_operation t9, t10", "stages": [{"name": "fetch", "start": 1030, "end": 1035, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1035, "end": 1055, "duration": 20, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1055, "end": 1065, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1065, "end": 1090, "duration": 25, "color": "#96CEB4"}], "total_start": 1030, "total_end": 1090, "total_duration": 60})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 28.00%; width: 16.00%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1035, "end": 1055, "duration": 20, "color": "#4ECDC4"}, {"id": 11, "pc": "0x000000001050", "instruction": "0x00000000fbb01456", "disassembly": "complex_operation t9, t10", "stages": [{"name": "fetch", "start": 1030, "end": 1035, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1035, "end": 1055, "duration": 20, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1055, "end": 1065, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1065, "end": 1090, "duration": 25, "color": "#96CEB4"}], "total_start": 1030, "total_end": 1090, "total_duration": 60})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 44.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1055, "end": 1065, "duration": 10, "color": "#45B7D1"}, {"id": 11, "pc": "0x000000001050", "instruction": "0x00000000fbb01456", "disassembly": "complex_operation t9, t10", "stages": [{"name": "fetch", "start": 1030, "end": 1035, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1035, "end": 1055, "duration": 20, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1055, "end": 1065, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1065, "end": 1090, "duration": 25, "color": "#96CEB4"}], "total_start": 1030, "total_end": 1090, "total_duration": 60})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 52.00%; width: 20.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1065, "end": 1090, "duration": 25, "color": "#96CEB4"}, {"id": 11, "pc": "0x000000001050", "instruction": "0x00000000fbb01456", "disassembly": "complex_operation t9, t10", "stages": [{"name": "fetch", "start": 1030, "end": 1035, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1035, "end": 1055, "duration": 20, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1055, "end": 1065, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1065, "end": 1090, "duration": 25, "color": "#96CEB4"}], "total_start": 1030, "total_end": 1090, "total_duration": 60})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001058</div>
                        <div class="instruction-disasm">vector_multiply t11, t12, t13</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 26.40%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1033, "end": 1038, "duration": 5, "color": "#FF6B6B"}, {"id": 12, "pc": "0x000000001058", "instruction": "0x00000000fbc01567", "disassembly": "vector_multiply t11, t12, t13", "stages": [{"name": "fetch", "start": 1033, "end": 1038, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1038, "end": 1060, "duration": 22, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1060, "end": 1070, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1070, "end": 1100, "duration": 30, "color": "#96CEB4"}], "total_start": 1033, "total_end": 1100, "total_duration": 67})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 30.40%; width: 17.60%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1038, "end": 1060, "duration": 22, "color": "#4ECDC4"}, {"id": 12, "pc": "0x000000001058", "instruction": "0x00000000fbc01567", "disassembly": "vector_multiply t11, t12, t13", "stages": [{"name": "fetch", "start": 1033, "end": 1038, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1038, "end": 1060, "duration": 22, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1060, "end": 1070, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1070, "end": 1100, "duration": 30, "color": "#96CEB4"}], "total_start": 1033, "total_end": 1100, "total_duration": 67})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 48.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1060, "end": 1070, "duration": 10, "color": "#45B7D1"}, {"id": 12, "pc": "0x000000001058", "instruction": "0x00000000fbc01567", "disassembly": "vector_multiply t11, t12, t13", "stages": [{"name": "fetch", "start": 1033, "end": 1038, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1038, "end": 1060, "duration": 22, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1060, "end": 1070, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1070, "end": 1100, "duration": 30, "color": "#96CEB4"}], "total_start": 1033, "total_end": 1100, "total_duration": 67})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 56.00%; width: 24.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1070, "end": 1100, "duration": 30, "color": "#96CEB4"}, {"id": 12, "pc": "0x000000001058", "instruction": "0x00000000fbc01567", "disassembly": "vector_multiply t11, t12, t13", "stages": [{"name": "fetch", "start": 1033, "end": 1038, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1038, "end": 1060, "duration": 22, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1060, "end": 1070, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1070, "end": 1100, "duration": 30, "color": "#96CEB4"}], "total_start": 1033, "total_end": 1100, "total_duration": 67})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001060</div>
                        <div class="instruction-disasm">matrix_transform t14, t15</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 28.80%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1036, "end": 1041, "duration": 5, "color": "#FF6B6B"}, {"id": 13, "pc": "0x000000001060", "instruction": "0x00000000fbd01678", "disassembly": "matrix_transform t14, t15", "stages": [{"name": "fetch", "start": 1036, "end": 1041, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1041, "end": 1065, "duration": 24, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1065, "end": 1075, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1075, "end": 1110, "duration": 35, "color": "#96CEB4"}], "total_start": 1036, "total_end": 1110, "total_duration": 74})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 32.80%; width: 19.20%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1041, "end": 1065, "duration": 24, "color": "#4ECDC4"}, {"id": 13, "pc": "0x000000001060", "instruction": "0x00000000fbd01678", "disassembly": "matrix_transform t14, t15", "stages": [{"name": "fetch", "start": 1036, "end": 1041, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1041, "end": 1065, "duration": 24, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1065, "end": 1075, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1075, "end": 1110, "duration": 35, "color": "#96CEB4"}], "total_start": 1036, "total_end": 1110, "total_duration": 74})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 52.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1065, "end": 1075, "duration": 10, "color": "#45B7D1"}, {"id": 13, "pc": "0x000000001060", "instruction": "0x00000000fbd01678", "disassembly": "matrix_transform t14, t15", "stages": [{"name": "fetch", "start": 1036, "end": 1041, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1041, "end": 1065, "duration": 24, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1065, "end": 1075, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1075, "end": 1110, "duration": 35, "color": "#96CEB4"}], "total_start": 1036, "total_end": 1110, "total_duration": 74})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 60.00%; width: 28.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1075, "end": 1110, "duration": 35, "color": "#96CEB4"}, {"id": 13, "pc": "0x000000001060", "instruction": "0x00000000fbd01678", "disassembly": "matrix_transform t14, t15", "stages": [{"name": "fetch", "start": 1036, "end": 1041, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1041, "end": 1065, "duration": 24, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1065, "end": 1075, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1075, "end": 1110, "duration": 35, "color": "#96CEB4"}], "total_start": 1036, "total_end": 1110, "total_duration": 74})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001068</div>
                        <div class="instruction-disasm">parallel_reduce t16, t17</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 31.20%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1039, "end": 1044, "duration": 5, "color": "#FF6B6B"}, {"id": 14, "pc": "0x000000001068", "instruction": "0x00000000fbe01789", "disassembly": "parallel_reduce t16, t17", "stages": [{"name": "fetch", "start": 1039, "end": 1044, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1044, "end": 1070, "duration": 26, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1070, "end": 1080, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1080, "end": 1120, "duration": 40, "color": "#96CEB4"}], "total_start": 1039, "total_end": 1120, "total_duration": 81})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 35.20%; width: 20.80%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1044, "end": 1070, "duration": 26, "color": "#4ECDC4"}, {"id": 14, "pc": "0x000000001068", "instruction": "0x00000000fbe01789", "disassembly": "parallel_reduce t16, t17", "stages": [{"name": "fetch", "start": 1039, "end": 1044, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1044, "end": 1070, "duration": 26, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1070, "end": 1080, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1080, "end": 1120, "duration": 40, "color": "#96CEB4"}], "total_start": 1039, "total_end": 1120, "total_duration": 81})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 56.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1070, "end": 1080, "duration": 10, "color": "#45B7D1"}, {"id": 14, "pc": "0x000000001068", "instruction": "0x00000000fbe01789", "disassembly": "parallel_reduce t16, t17", "stages": [{"name": "fetch", "start": 1039, "end": 1044, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1044, "end": 1070, "duration": 26, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1070, "end": 1080, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1080, "end": 1120, "duration": 40, "color": "#96CEB4"}], "total_start": 1039, "total_end": 1120, "total_duration": 81})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 64.00%; width: 32.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1080, "end": 1120, "duration": 40, "color": "#96CEB4"}, {"id": 14, "pc": "0x000000001068", "instruction": "0x00000000fbe01789", "disassembly": "parallel_reduce t16, t17", "stages": [{"name": "fetch", "start": 1039, "end": 1044, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1044, "end": 1070, "duration": 26, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1070, "end": 1080, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1080, "end": 1120, "duration": 40, "color": "#96CEB4"}], "total_start": 1039, "total_end": 1120, "total_duration": 81})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            

                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-pc">0x000000001070</div>
                        <div class="instruction-disasm">sync_barrier</div>
                    </div>
                    <div class="timeline-bars">
                        
                    <div class="stage-bar fetch"
                         style="left: 33.60%; width: 4.00%; background-color: #FF6B6B;"
                         onmouseenter="showTooltip(event, {"name": "fetch", "start": 1042, "end": 1047, "duration": 5, "color": "#FF6B6B"}, {"id": 15, "pc": "0x000000001070", "instruction": "0x00000000fbf0189a", "disassembly": "sync_barrier", "stages": [{"name": "fetch", "start": 1042, "end": 1047, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1047, "end": 1075, "duration": 28, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1075, "end": 1085, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1125, "duration": 40, "color": "#96CEB4"}], "total_start": 1042, "total_end": 1125, "total_duration": 83})"
                         onmouseleave="hideTooltip()">
                        fetch
                    </div>
                
                    <div class="stage-bar decode"
                         style="left: 37.60%; width: 22.40%; background-color: #4ECDC4;"
                         onmouseenter="showTooltip(event, {"name": "decode", "start": 1047, "end": 1075, "duration": 28, "color": "#4ECDC4"}, {"id": 15, "pc": "0x000000001070", "instruction": "0x00000000fbf0189a", "disassembly": "sync_barrier", "stages": [{"name": "fetch", "start": 1042, "end": 1047, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1047, "end": 1075, "duration": 28, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1075, "end": 1085, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1125, "duration": 40, "color": "#96CEB4"}], "total_start": 1042, "total_end": 1125, "total_duration": 83})"
                         onmouseleave="hideTooltip()">
                        decode
                    </div>
                
                    <div class="stage-bar dispatch"
                         style="left: 60.00%; width: 8.00%; background-color: #45B7D1;"
                         onmouseenter="showTooltip(event, {"name": "dispatch", "start": 1075, "end": 1085, "duration": 10, "color": "#45B7D1"}, {"id": 15, "pc": "0x000000001070", "instruction": "0x00000000fbf0189a", "disassembly": "sync_barrier", "stages": [{"name": "fetch", "start": 1042, "end": 1047, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1047, "end": 1075, "duration": 28, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1075, "end": 1085, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1125, "duration": 40, "color": "#96CEB4"}], "total_start": 1042, "total_end": 1125, "total_duration": 83})"
                         onmouseleave="hideTooltip()">
                        dispatch
                    </div>
                
                    <div class="stage-bar execute"
                         style="left: 68.00%; width: 32.00%; background-color: #96CEB4;"
                         onmouseenter="showTooltip(event, {"name": "execute", "start": 1085, "end": 1125, "duration": 40, "color": "#96CEB4"}, {"id": 15, "pc": "0x000000001070", "instruction": "0x00000000fbf0189a", "disassembly": "sync_barrier", "stages": [{"name": "fetch", "start": 1042, "end": 1047, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1047, "end": 1075, "duration": 28, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1075, "end": 1085, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1125, "duration": 40, "color": "#96CEB4"}], "total_start": 1042, "total_end": 1125, "total_duration": 83})"
                         onmouseleave="hideTooltip()">
                        execute
                    </div>
                
                    </div>
                </div>
            
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <div class="summary">
        <h3>执行统计摘要</h3>
        <div class="summary-grid">
            
            <div class="summary-item">
                <div class="summary-value">15</div>
                <div class="summary-label">总指令数</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">46.3</div>
                <div class="summary-label">平均执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">83</div>
                <div class="summary-label">最长执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">20</div>
                <div class="summary-label">最短执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.0</div>
                <div class="summary-label">平均Fetch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">14.6</div>
                <div class="summary-label">平均Decode时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">9.3</div>
                <div class="summary-label">平均Dispatch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">17.5</div>
                <div class="summary-label">平均Execute时间</div>
            </div>
        
        </div>
    </div>

    <script>
        const instructions = [{"id": 1, "pc": "0x000000001000", "instruction": "0x00000000fb123456", "disassembly": "tld.trii.linear.u32.global t5, (x10)", "stages": [{"name": "fetch", "start": 1000, "end": 1005, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1005, "end": 1010, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1010, "end": 1015, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1015, "end": 1020, "duration": 5, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1020, "total_duration": 20}, {"id": 2, "pc": "0x000000001008", "instruction": "0x00000000fb234567", "disassembly": "tmma.ttt t3, t1, t2", "stages": [{"name": "fetch", "start": 1003, "end": 1008, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1008, "end": 1013, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1013, "end": 1018, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1018, "end": 1030, "duration": 12, "color": "#96CEB4"}], "total_start": 1003, "total_end": 1030, "total_duration": 27}, {"id": 3, "pc": "0x000000001010", "instruction": "0x00000000fb345678", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 1006, "end": 1011, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1011, "end": 1016, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1016, "end": 1025, "duration": 9, "color": "#45B7D1"}, {"name": "execute", "start": 1025, "end": 1035, "duration": 10, "color": "#96CEB4"}], "total_start": 1006, "total_end": 1035, "total_duration": 29}, {"id": 4, "pc": "0x000000001018", "instruction": "0x00000000fb456789", "disassembly": "tld.linear.u16 t6, (x11)", "stages": [{"name": "fetch", "start": 1009, "end": 1014, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1014, "end": 1020, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1020, "end": 1030, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1030, "end": 1040, "duration": 10, "color": "#96CEB4"}], "total_start": 1009, "total_end": 1040, "total_duration": 31}, {"id": 5, "pc": "0x000000001020", "instruction": "0x00000000fb567890", "disassembly": "tmma.tnt t4, t2, t3", "stages": [{"name": "fetch", "start": 1012, "end": 1017, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1017, "end": 1025, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1025, "end": 1035, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1035, "end": 1050, "duration": 15, "color": "#96CEB4"}], "total_start": 1012, "total_end": 1050, "total_duration": 38}, {"id": 6, "pc": "0x000000001028", "instruction": "0x00000000fb678901", "disassembly": "tst.linear.u32 (x12), t7", "stages": [{"name": "fetch", "start": 1015, "end": 1020, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1020, "end": 1030, "duration": 10, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1030, "end": 1040, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1040, "end": 1045, "duration": 5, "color": "#96CEB4"}], "total_start": 1015, "total_end": 1045, "total_duration": 30}, {"id": 7, "pc": "0x000000001030", "instruction": "0x00000000fb789012", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 1018, "end": 1023, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1023, "end": 1035, "duration": 12, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1035, "end": 1045, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1045, "end": 1050, "duration": 5, "color": "#96CEB4"}], "total_start": 1018, "total_end": 1050, "total_duration": 32}, {"id": 8, "pc": "0x000000001038", "instruction": "0x00000000fb890123", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1021, "end": 1026, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1026, "end": 1040, "duration": 14, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1040, "end": 1050, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1050, "end": 1055, "duration": 5, "color": "#96CEB4"}], "total_start": 1021, "total_end": 1055, "total_duration": 34}, {"id": 9, "pc": "0x000000001040", "instruction": "0x00000000fb901234", "disassembly": "tld.trii.linear.u64.global t8, (x13)", "stages": [{"name": "fetch", "start": 1024, "end": 1029, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1029, "end": 1045, "duration": 16, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1045, "end": 1055, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1055, "end": 1065, "duration": 10, "color": "#96CEB4"}], "total_start": 1024, "total_end": 1065, "total_duration": 41}, {"id": 10, "pc": "0x000000001048", "instruction": "0x00000000fba01345", "disassembly": "tmma.ttn t5, t6, t7", "stages": [{"name": "fetch", "start": 1027, "end": 1032, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1032, "end": 1050, "duration": 18, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1050, "end": 1060, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1060, "end": 1075, "duration": 15, "color": "#96CEB4"}], "total_start": 1027, "total_end": 1075, "total_duration": 48}, {"id": 11, "pc": "0x000000001050", "instruction": "0x00000000fbb01456", "disassembly": "complex_operation t9, t10", "stages": [{"name": "fetch", "start": 1030, "end": 1035, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1035, "end": 1055, "duration": 20, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1055, "end": 1065, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1065, "end": 1090, "duration": 25, "color": "#96CEB4"}], "total_start": 1030, "total_end": 1090, "total_duration": 60}, {"id": 12, "pc": "0x000000001058", "instruction": "0x00000000fbc01567", "disassembly": "vector_multiply t11, t12, t13", "stages": [{"name": "fetch", "start": 1033, "end": 1038, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1038, "end": 1060, "duration": 22, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1060, "end": 1070, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1070, "end": 1100, "duration": 30, "color": "#96CEB4"}], "total_start": 1033, "total_end": 1100, "total_duration": 67}, {"id": 13, "pc": "0x000000001060", "instruction": "0x00000000fbd01678", "disassembly": "matrix_transform t14, t15", "stages": [{"name": "fetch", "start": 1036, "end": 1041, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1041, "end": 1065, "duration": 24, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1065, "end": 1075, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1075, "end": 1110, "duration": 35, "color": "#96CEB4"}], "total_start": 1036, "total_end": 1110, "total_duration": 74}, {"id": 14, "pc": "0x000000001068", "instruction": "0x00000000fbe01789", "disassembly": "parallel_reduce t16, t17", "stages": [{"name": "fetch", "start": 1039, "end": 1044, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1044, "end": 1070, "duration": 26, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1070, "end": 1080, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1080, "end": 1120, "duration": 40, "color": "#96CEB4"}], "total_start": 1039, "total_end": 1120, "total_duration": 81}, {"id": 15, "pc": "0x000000001070", "instruction": "0x00000000fbf0189a", "disassembly": "sync_barrier", "stages": [{"name": "fetch", "start": 1042, "end": 1047, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1047, "end": 1075, "duration": 28, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1075, "end": 1085, "duration": 10, "color": "#45B7D1"}, {"name": "execute", "start": 1085, "end": 1125, "duration": 40, "color": "#96CEB4"}], "total_start": 1042, "total_end": 1125, "total_duration": 83}];
        const minTime = 1000;
        const maxTime = 1125;
        const timeRange = maxTime - minTime;

        let currentZoom = 1.0;
        let currentFilter = '';
        let currentSort = 'id';
        let currentScrollLeft = 0;

        // 初始化时间轴
        function initTimeAxis() {
            const timeAxis = document.getElementById('timeAxis');
            timeAxis.innerHTML = ''; // 清空现有内容
            const tickCount = 10;

            for (let i = 0; i <= tickCount; i++) {
                const time = minTime + (timeRange * i / tickCount);
                const position = (i / tickCount) * 100 * currentZoom;

                const tick = document.createElement('div');
                tick.className = 'time-tick';
                tick.style.left = position + '%';
                timeAxis.appendChild(tick);

                const label = document.createElement('div');
                label.className = 'time-label';
                label.style.left = position + '%';
                label.textContent = Math.round(time);
                timeAxis.appendChild(label);
            }
        }

        // 计算时间条的位置和宽度
        function calculateBarPosition(start, end) {
            const startPercent = ((start - minTime) / timeRange) * 100 * currentZoom;
            const widthPercent = ((end - start) / timeRange) * 100 * currentZoom;
            return { left: startPercent, width: Math.max(widthPercent, 0.5) };
        }

        // 显示工具提示
        function showTooltip(event, stage, instruction) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${stage.name.toUpperCase()}</strong><br>
                指令: ${instruction.disassembly}<br>
                开始: ${stage.start}<br>
                结束: ${stage.end}<br>
                持续: ${stage.duration}
            `;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.style.opacity = '1';
        }

        // 隐藏工具提示
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }

        // 渲染指令列表
        function renderInstructions(filteredInstructions = null) {
            const instructionList = document.getElementById('instructionList');
            const instructionsToRender = filteredInstructions || instructions;

            instructionList.innerHTML = '';

            instructionsToRender.forEach(instruction => {
                const row = document.createElement('div');
                row.className = 'instruction-row';
                // 设置行的最小宽度以匹配当前缩放
                row.style.minWidth = (800 * currentZoom) + 'px';

                const info = document.createElement('div');
                info.className = 'instruction-info';
                info.innerHTML = `
                    <div class="instruction-pc">${instruction.pc}</div>
                    <div class="instruction-disasm">${instruction.disassembly}</div>
                `;

                const timeline = document.createElement('div');
                timeline.className = 'timeline-bars';

                instruction.stages.forEach(stage => {
                    const bar = document.createElement('div');
                    bar.className = `stage-bar ${stage.name}`;
                    bar.style.backgroundColor = stage.color;

                    const position = calculateBarPosition(stage.start, stage.end);
                    bar.style.left = position.left + '%';
                    bar.style.width = position.width + '%';

                    if (position.width > 2) {
                        bar.textContent = stage.name;
                    }

                    bar.addEventListener('mouseenter', (e) => showTooltip(e, stage, instruction));
                    bar.addEventListener('mouseleave', hideTooltip);

                    timeline.appendChild(bar);
                });

                row.appendChild(info);
                row.appendChild(timeline);
                instructionList.appendChild(row);
            });
        }

        // 过滤指令
        function filterInstructions() {
            const filter = document.getElementById('filterInput').value.toLowerCase();
            if (!filter) {
                renderInstructions();
                return;
            }

            const filtered = instructions.filter(instr =>
                instr.pc.toLowerCase().includes(filter) ||
                instr.disassembly.toLowerCase().includes(filter)
            );

            renderInstructions(filtered);
        }

        // 排序指令
        function sortInstructions() {
            const sortBy = document.getElementById('sortSelect').value;
            const sorted = [...instructions];

            sorted.sort((a, b) => {
                switch(sortBy) {
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }
            });

            renderInstructions(sorted);
        }

        // 缩放功能
        function updateZoom() {
            const zoom = parseFloat(document.getElementById('zoomSlider').value);
            currentZoom = zoom;
            document.getElementById('zoomValue').textContent = zoom.toFixed(1) + 'x';

            // 更新时间轴和时间线容器的宽度以支持水平滚动
            const newWidth = (800 * zoom) + 'px';

            const timeAxis = document.getElementById('timeAxis');
            timeAxis.style.minWidth = newWidth;

            const timelineBars = document.querySelectorAll('.timeline-bars');
            timelineBars.forEach(bar => {
                bar.style.minWidth = newWidth;
            });

            // 更新指令行的最小宽度以确保背景扩展
            const instructionRows = document.querySelectorAll('.instruction-row');
            instructionRows.forEach(row => {
                row.style.minWidth = newWidth;
            });

            // 重新渲染时间轴和指令以应用缩放
            initTimeAxis();
            renderInstructions();
        }

        // 重置视图
        function resetView() {
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('filterInput').value = '';
            document.getElementById('sortSelect').value = 'id';
            currentScrollLeft = 0;
            updateZoom();
            renderInstructions();
            // 重置滚动位置
            const timelineContainer = document.querySelector('.timeline-container');
            timelineContainer.scrollLeft = 0;
        }

        // 键盘快捷键处理
        function handleKeyPress(event) {
            // 如果用户正在输入框中输入，不处理快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {
                return;
            }

            const zoomSlider = document.getElementById('zoomSlider');
            const timelineContainer = document.querySelector('.timeline-container');
            const scrollStep = 50; // 滚动步长
            const zoomStep = 0.2; // 缩放步长

            switch(event.key.toLowerCase()) {
                case 'w': // 放大
                    event.preventDefault();
                    const newZoomUp = Math.min(parseFloat(zoomSlider.value) + zoomStep, 20);
                    zoomSlider.value = newZoomUp;
                    updateZoom();
                    break;

                case 's': // 缩小
                    event.preventDefault();
                    const newZoomDown = Math.max(parseFloat(zoomSlider.value) - zoomStep, 0.1);
                    zoomSlider.value = newZoomDown;
                    updateZoom();
                    break;

                case 'a': // 左移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.max(timelineContainer.scrollLeft - scrollStep, 0);
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'd': // 右移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.min(
                        timelineContainer.scrollLeft + scrollStep,
                        timelineContainer.scrollWidth - timelineContainer.clientWidth
                    );
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'r': // 复位缩放
                    event.preventDefault();
                    zoomSlider.value = 1;
                    updateZoom();
                    timelineContainer.scrollLeft = 0;
                    currentScrollLeft = 0;
                    break;
            }
        }

        // 事件监听器
        document.getElementById('zoomSlider').addEventListener('input', updateZoom);
        document.getElementById('filterInput').addEventListener('input', filterInstructions);
        document.getElementById('sortSelect').addEventListener('change', sortInstructions);

        // 键盘事件监听器
        document.addEventListener('keydown', handleKeyPress);

        // 初始化
        initTimeAxis();
        renderInstructions();
    </script>
</body>
</html>
        
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试脚本
"""

import os
import sys

def test_file_reading():
    print("测试文件读取...")
    
    # 检查文件是否存在
    filename = "test_with_comments_and_errors.log"
    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在")
        return False
    
    print(f"文件存在，大小: {os.path.getsize(filename)} 字节")
    
    # 读取文件内容
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"文件共 {len(lines)} 行")
        
        # 显示前几行
        print("前5行内容:")
        for i, line in enumerate(lines[:5]):
            print(f"  {i+1}: {line.strip()}")
        
        return True
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return False

def test_comment_filtering():
    print("\n测试注释过滤...")
    
    filename = "test_with_comments_and_errors.log"
    try:
        processed_lines = []
        comment_count = 0
        
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue  # 跳过空行
                if line.startswith('//'):
                    comment_count += 1
                    continue  # 跳过注释行
                processed_lines.append(line)
        
        print(f"跳过了 {comment_count} 行注释")
        print(f"处理后剩余 {len(processed_lines)} 行")
        
        # 显示处理后的前几行
        print("处理后的前3行:")
        for i, line in enumerate(processed_lines[:3]):
            print(f"  {i+1}: {line}")
        
        return True
    except Exception as e:
        print(f"处理注释时出错: {e}")
        return False

def test_pandas_reading():
    print("\n测试pandas读取...")
    
    try:
        import pandas as pd
        
        # 创建临时文件
        filename = "test_with_comments_and_errors.log"
        processed_lines = []
        
        with open(filename, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('//'):
                    continue
                processed_lines.append(line)
        
        # 写入临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8') as temp_f:
            temp_f.write('\n'.join(processed_lines))
            temp_filename = temp_f.name
        
        print(f"临时文件: {temp_filename}")
        
        # 读取CSV
        df = pd.read_csv(temp_filename)
        print(f"成功读取 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        
        # 清理临时文件
        os.unlink(temp_filename)
        
        return True
    except Exception as e:
        print(f"pandas读取时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("开始调试测试...")
    
    if test_file_reading():
        if test_comment_filtering():
            test_pandas_reading()
    
    print("\n调试测试完成")

if __name__ == "__main__":
    main()

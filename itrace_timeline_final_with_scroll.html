
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }

        .timeline-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            width: 100%;
            max-width: 100%;
            display: flex;
            flex-direction: column;
        }

        .timeline-content {
            display: flex;
            position: relative;
            width: 100%;
        }

        .fixed-left-panel {
            width: 350px;
            flex-shrink: 0;
            background: #fafafa;
            border-right: 2px solid #e0e0e0;
            z-index: 20;
            position: relative;
        }

        .scrollable-right-panel {
            flex: 1;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
        }

        #instructionList {
            position: relative;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-left: auto;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .time-axis {
            position: relative;
            height: 40px;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            min-width: 800px;
            overflow: visible;
        }

        .time-tick {
            position: absolute;
            bottom: 0;
            width: 1px;
            height: 10px;
            background: #666;
        }

        .time-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: translateX(-50%);
        }

        .instruction-row {
            display: flex;
            align-items: stretch;
            margin-bottom: 8px;
            min-height: 45px;
            position: relative;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .instruction-left {
            width: 350px;
            padding: 8px;
            background: #fafafa;
            border-radius: 8px 0 0 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 45px;
            box-sizing: border-box;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .instruction-left:hover {
            background: #e3f2fd;
        }

        .instruction-left:active {
            background: #bbdefb;
        }

        .instruction-left.highlighted,
        .instruction-right.highlighted {
            background: #fff3cd !important;
            border: 2px solid #ffc107 !important;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5) !important;
            animation: highlight-pulse 0.5s ease-in-out;
        }

        @keyframes highlight-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .instruction-right {
            flex: 1;
            background: #fafafa;
            border-radius: 0 8px 8px 0;
            position: relative;
            min-width: 800px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        .instruction-row:hover {
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .instruction-info {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .instruction-pc {
            color: #666;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .instruction-disasm {
            color: #2c3e50;
            font-family: 'Courier New', monospace;
            font-size: 1.0em;
            font-weight: bold;
            word-break: break-all;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 4px 8px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }

        .timeline-bars {
            position: relative;
            width: 100%;
            margin: 8px;
            overflow: visible;
            flex: 1;
            min-height: 29px;
        }

        .stage-bar {
            position: absolute;
            height: 20px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.7);
            cursor: pointer;
            transition: all 0.2s ease;
            top: 50%;
            transform: translateY(-50%);
            border: 1px solid rgba(0,0,0,0.2);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .stage-bar:hover {
            transform: translateY(-50%) scale(1.05);
            z-index: 100 !important;
            box-shadow: 0 3px 6px rgba(0,0,0,0.4);
            border: 2px solid rgba(255,255,255,0.8);
        }

        /* 所有阶段都在同一高度 - 垂直居中 */
        .stage-bar.fetch {
            z-index: 4;
        }
        .stage-bar.decode {
            z-index: 3;
        }
        .stage-bar.dispatch {
            z-index: 2;
        }
        .stage-bar.execute {
            z-index: 1;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            color: #555;
        }

        .control-group input, .control-group select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .summary {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .summary h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .summary-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }

        .summary-label {
            color: #666;
            margin-top: 5px;
        }

        .worst-5-percent-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .worst-5-percent-section h3 {
            margin-top: 0;
            color: #dc3545;
            border-bottom: 2px solid #dc3545;
            padding-bottom: 10px;
        }

        .worst-instructions-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .worst-instruction-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .worst-instruction-item:hover {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            transform: translateX(5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .worst-instruction-item:active {
            transform: translateX(3px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .worst-rank {
            font-size: 1.2em;
            font-weight: bold;
            color: #dc3545;
            min-width: 40px;
            text-align: center;
            margin-right: 15px;
        }

        .worst-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .worst-pc {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #666;
            font-weight: bold;
        }

        .worst-disasm {
            font-family: 'Courier New', monospace;
            font-size: 1.0em;
            color: #2c3e50;
            font-weight: bold;
        }

        .worst-duration {
            font-size: 0.9em;
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>100</strong></div>
            <div>时间范围: <strong>1000 - 5556</strong></div>
            <div>总执行时间: <strong>4556</strong></div>
            <div>生成时间: <strong>2025-08-07 16:35:57</strong></div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline-header">
            <h3 style="margin: 0;">指令执行时间线</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>Fetch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ECDC4;"></div>
                    <span>Decode</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>Dispatch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96CEB4;"></div>
                    <span>Execute</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>缩放:</label>
                <input type="range" id="zoomSlider" min="0.1" max="100" step="0.1" value="1">
                <span id="zoomValue">1.0x</span>
            </div>
            <div class="control-group">
                <label>过滤指令:</label>
                <input type="text" id="filterInput" placeholder="输入PC地址或反汇编代码...">
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortSelect">
                    <option value="id">指令ID</option>
                    <option value="start_time">开始时间</option>
                    <option value="duration">执行时长</option>
                </select>
            </div>
            <button class="btn" onclick="resetView()">重置视图</button>
            <div class="control-group">
                <label>操作说明:</label>
                <span style="font-size: 12px; color: #666;">W/S: 放大/缩小 | A/D: 左移/右移 | R: 复位缩放 | 鼠标拖拽: 上拖放大/下拖缩小 | 点击指令名: 居中显示</span>
            </div>
        </div>

        <div class="timeline-content">
            <div class="fixed-left-panel">
                <div style="height: 40px; margin-bottom: 20px;"></div>
                <div id="leftPanelContent"></div>
            </div>
            <div class="scrollable-right-panel">
                <div class="time-axis" id="timeAxis"></div>
                <div id="instructionList"></div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <div class="summary">
        <h3>执行统计摘要</h3>
        <div class="summary-grid">
            
            <div class="summary-item">
                <div class="summary-value">100</div>
                <div class="summary-label">总指令数</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">35.4</div>
                <div class="summary-label">平均执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">102</div>
                <div class="summary-label">最长执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">19</div>
                <div class="summary-label">最短执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.4</div>
                <div class="summary-label">平均Fetch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.5</div>
                <div class="summary-label">平均Decode时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">5.6</div>
                <div class="summary-label">平均Dispatch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">19.0</div>
                <div class="summary-label">平均Execute时间</div>
            </div>
        
        </div>
    </div>

    <div class="worst-5-percent-section">
        <h3>Worst 5% 执行时间指令 (5 条)</h3>
        <div class="worst-instructions-list">
            
                <div class="worst-instruction-item" onclick="jumpToInstruction(80)">
                    <div class="worst-rank">#1</div>
                    <div class="worst-info">
                        <div class="worst-pc">0x000000000668</div>
                        <div class="worst-disasm">ace_bsync x25</div>
                        <div class="worst-duration">102 时间单位</div>
                    </div>
                </div>
            
                <div class="worst-instruction-item" onclick="jumpToInstruction(40)">
                    <div class="worst-rank">#2</div>
                    <div class="worst-info">
                        <div class="worst-pc">0x000000000528</div>
                        <div class="worst-disasm">tmma.ntt t4, t15, t23</div>
                        <div class="worst-duration">99 时间单位</div>
                    </div>
                </div>
            
                <div class="worst-instruction-item" onclick="jumpToInstruction(100)">
                    <div class="worst-rank">#3</div>
                    <div class="worst-info">
                        <div class="worst-pc">0x000000000708</div>
                        <div class="worst-disasm">tmma.nnn t14, t24, t28</div>
                        <div class="worst-duration">95 时间单位</div>
                    </div>
                </div>
            
                <div class="worst-instruction-item" onclick="jumpToInstruction(60)">
                    <div class="worst-rank">#4</div>
                    <div class="worst-info">
                        <div class="worst-pc">0x0000000005c8</div>
                        <div class="worst-disasm">tmma.tnt t18, t21, t5</div>
                        <div class="worst-duration">81 时间单位</div>
                    </div>
                </div>
            
                <div class="worst-instruction-item" onclick="jumpToInstruction(20)">
                    <div class="worst-rank">#5</div>
                    <div class="worst-info">
                        <div class="worst-pc">0x000000000488</div>
                        <div class="worst-disasm">tld.global.u32 t15, (x5)</div>
                        <div class="worst-duration">71 时间单位</div>
                    </div>
                </div>
            
        
        </div>
    </div>

    <script>
        const instructions = [{"id": 1, "pc": "0x0000000003f0", "instruction": "0xb3b1799d", "disassembly": "tmma.tnt t1, t17, t15", "stages": [{"name": "fetch", "start": 1000, "end": 1004, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 1004, "end": 1008, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1008, "end": 1016, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 1016, "end": 1024, "duration": 8, "color": "#96CEB4"}], "total_start": 1000, "total_end": 1024, "total_duration": 24}, {"id": 2, "pc": "0x0000000003f8", "instruction": "0xcd9c66b3", "disassembly": "tld.linear.u8 t5, (x27)", "stages": [{"name": "fetch", "start": 1039, "end": 1042, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 1042, "end": 1045, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1045, "end": 1048, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 1048, "end": 1059, "duration": 11, "color": "#96CEB4"}], "total_start": 1039, "total_end": 1059, "total_duration": 20}, {"id": 3, "pc": "0x000000000400", "instruction": "0x915ef6d1", "disassembly": "tmma.nnn t1, t12, t26", "stages": [{"name": "fetch", "start": 1067, "end": 1071, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 1071, "end": 1077, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1077, "end": 1084, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 1084, "end": 1097, "duration": 13, "color": "#96CEB4"}], "total_start": 1067, "total_end": 1097, "total_duration": 30}, {"id": 4, "pc": "0x000000000408", "instruction": "0xd241330b", "disassembly": "tst.shared.u64 (x27), t21", "stages": [{"name": "fetch", "start": 1102, "end": 1107, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1107, "end": 1111, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1111, "end": 1115, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 1115, "end": 1130, "duration": 15, "color": "#96CEB4"}], "total_start": 1102, "total_end": 1130, "total_duration": 28}, {"id": 5, "pc": "0x000000000410", "instruction": "0x27be3111", "disassembly": "ace_bsync x6", "stages": [{"name": "fetch", "start": 1136, "end": 1141, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1141, "end": 1146, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1146, "end": 1153, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 1153, "end": 1166, "duration": 13, "color": "#96CEB4"}], "total_start": 1136, "total_end": 1166, "total_duration": 30}, {"id": 6, "pc": "0x000000000418", "instruction": "0xcacfb3d0", "disassembly": "twait", "stages": [{"name": "fetch", "start": 1171, "end": 1178, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 1178, "end": 1181, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1181, "end": 1187, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 1187, "end": 1194, "duration": 7, "color": "#96CEB4"}], "total_start": 1171, "total_end": 1194, "total_duration": 23}, {"id": 7, "pc": "0x000000000420", "instruction": "0x5b0dbb41", "disassembly": "tmma.nnn t23, t12, t4", "stages": [{"name": "fetch", "start": 1207, "end": 1210, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 1210, "end": 1218, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1218, "end": 1222, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 1222, "end": 1236, "duration": 14, "color": "#96CEB4"}], "total_start": 1207, "total_end": 1236, "total_duration": 29}, {"id": 8, "pc": "0x000000000428", "instruction": "0xeaf61a26", "disassembly": "tcsrw.i 0x6", "stages": [{"name": "fetch", "start": 1242, "end": 1248, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 1248, "end": 1253, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1253, "end": 1259, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 1259, "end": 1284, "duration": 25, "color": "#96CEB4"}], "total_start": 1242, "total_end": 1284, "total_duration": 42}, {"id": 9, "pc": "0x000000000430", "instruction": "0x39a3b2e9", "disassembly": "tmma.ntt t22, t13, t17", "stages": [{"name": "fetch", "start": 1294, "end": 1302, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 1302, "end": 1310, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1310, "end": 1318, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 1318, "end": 1325, "duration": 7, "color": "#96CEB4"}], "total_start": 1294, "total_end": 1325, "total_duration": 31}, {"id": 10, "pc": "0x000000000438", "instruction": "0xb28defe3", "disassembly": "tst.shared.u64 (x15), t10", "stages": [{"name": "fetch", "start": 1339, "end": 1345, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 1345, "end": 1351, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1351, "end": 1356, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1356, "end": 1401, "duration": 45, "color": "#96CEB4"}], "total_start": 1339, "total_end": 1401, "total_duration": 62}, {"id": 11, "pc": "0x000000000440", "instruction": "0x4838b326", "disassembly": "tmma.ntt t3, t14, t2", "stages": [{"name": "fetch", "start": 1414, "end": 1419, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 1419, "end": 1425, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1425, "end": 1430, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1430, "end": 1437, "duration": 7, "color": "#96CEB4"}], "total_start": 1414, "total_end": 1437, "total_duration": 23}, {"id": 12, "pc": "0x000000000448", "instruction": "0xf9c349e0", "disassembly": "tmma.nnn t20, t13, t31", "stages": [{"name": "fetch", "start": 1445, "end": 1451, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 1451, "end": 1459, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1459, "end": 1465, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 1465, "end": 1474, "duration": 9, "color": "#96CEB4"}], "total_start": 1445, "total_end": 1474, "total_duration": 29}, {"id": 13, "pc": "0x000000000450", "instruction": "0x33bed01d", "disassembly": "tcsrw.i 0x16", "stages": [{"name": "fetch", "start": 1483, "end": 1491, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 1491, "end": 1498, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1498, "end": 1504, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 1504, "end": 1527, "duration": 23, "color": "#96CEB4"}], "total_start": 1483, "total_end": 1527, "total_duration": 44}, {"id": 14, "pc": "0x000000000458", "instruction": "0x6cabcc97", "disassembly": "tcsrw.i 0x8", "stages": [{"name": "fetch", "start": 1538, "end": 1545, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 1545, "end": 1551, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1551, "end": 1554, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 1554, "end": 1560, "duration": 6, "color": "#96CEB4"}], "total_start": 1538, "total_end": 1560, "total_duration": 22}, {"id": 15, "pc": "0x000000000460", "instruction": "0x37209bdf", "disassembly": "tst.shared.u64 (x27), t4", "stages": [{"name": "fetch", "start": 1566, "end": 1572, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 1572, "end": 1578, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1578, "end": 1585, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 1585, "end": 1604, "duration": 19, "color": "#96CEB4"}], "total_start": 1566, "total_end": 1604, "total_duration": 38}, {"id": 16, "pc": "0x000000000468", "instruction": "0x505cacec", "disassembly": "tld.linear.u8 t0, (x7)", "stages": [{"name": "fetch", "start": 1617, "end": 1625, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 1625, "end": 1632, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1632, "end": 1637, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1637, "end": 1662, "duration": 25, "color": "#96CEB4"}], "total_start": 1617, "total_end": 1662, "total_duration": 45}, {"id": 17, "pc": "0x000000000470", "instruction": "0x2c8eaee9", "disassembly": "tld.texture.u16 t27, (x10)", "stages": [{"name": "fetch", "start": 1672, "end": 1678, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 1678, "end": 1681, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1681, "end": 1689, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 1689, "end": 1702, "duration": 13, "color": "#96CEB4"}], "total_start": 1672, "total_end": 1702, "total_duration": 30}, {"id": 18, "pc": "0x000000000478", "instruction": "0xd30ff46e", "disassembly": "tst.shared.u64 (x6), t19", "stages": [{"name": "fetch", "start": 1715, "end": 1723, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 1723, "end": 1730, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1730, "end": 1737, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 1737, "end": 1748, "duration": 11, "color": "#96CEB4"}], "total_start": 1715, "total_end": 1748, "total_duration": 33}, {"id": 19, "pc": "0x000000000480", "instruction": "0x6fb8d16c", "disassembly": "tst.shared.u64 (x0), t20", "stages": [{"name": "fetch", "start": 1755, "end": 1761, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 1761, "end": 1764, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1764, "end": 1767, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 1767, "end": 1783, "duration": 16, "color": "#96CEB4"}], "total_start": 1755, "total_end": 1783, "total_duration": 28}, {"id": 20, "pc": "0x000000000488", "instruction": "0x4d4cbf37", "disassembly": "tld.global.u32 t15, (x5)", "stages": [{"name": "fetch", "start": 1792, "end": 1795, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 1795, "end": 1803, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1803, "end": 1809, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 1809, "end": 1863, "duration": 54, "color": "#96CEB4"}], "total_start": 1792, "total_end": 1863, "total_duration": 71}, {"id": 21, "pc": "0x000000000490", "instruction": "0xd40db9b4", "disassembly": "tst.shared.u64 (x8), t30", "stages": [{"name": "fetch", "start": 1876, "end": 1883, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 1883, "end": 1887, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1887, "end": 1892, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 1892, "end": 1913, "duration": 21, "color": "#96CEB4"}], "total_start": 1876, "total_end": 1913, "total_duration": 37}, {"id": 22, "pc": "0x000000000498", "instruction": "0x7c52c49f", "disassembly": "tcsrw.i 0x12", "stages": [{"name": "fetch", "start": 1927, "end": 1935, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 1935, "end": 1940, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1940, "end": 1946, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 1946, "end": 1971, "duration": 25, "color": "#96CEB4"}], "total_start": 1927, "total_end": 1971, "total_duration": 44}, {"id": 23, "pc": "0x0000000004a0", "instruction": "0x802753a1", "disassembly": "tld.linear.u8 t28, (x7)", "stages": [{"name": "fetch", "start": 1981, "end": 1985, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 1985, "end": 1989, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 1989, "end": 1992, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 1992, "end": 2007, "duration": 15, "color": "#96CEB4"}], "total_start": 1981, "total_end": 2007, "total_duration": 26}, {"id": 24, "pc": "0x0000000004a8", "instruction": "0xa69b6662", "disassembly": "tld.linear.u8 t14, (x14)", "stages": [{"name": "fetch", "start": 2012, "end": 2015, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2015, "end": 2018, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2018, "end": 2026, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 2026, "end": 2051, "duration": 25, "color": "#96CEB4"}], "total_start": 2012, "total_end": 2051, "total_duration": 39}, {"id": 25, "pc": "0x0000000004b0", "instruction": "0x4a9bedd4", "disassembly": "tmma.tnt t2, t21, t4", "stages": [{"name": "fetch", "start": 2056, "end": 2063, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 2063, "end": 2067, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2067, "end": 2072, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 2072, "end": 2092, "duration": 20, "color": "#96CEB4"}], "total_start": 2056, "total_end": 2092, "total_duration": 36}, {"id": 26, "pc": "0x0000000004b8", "instruction": "0x9a0b3c33", "disassembly": "tst.shared.u64 (x30), t15", "stages": [{"name": "fetch", "start": 2100, "end": 2106, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 2106, "end": 2112, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2112, "end": 2116, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2116, "end": 2124, "duration": 8, "color": "#96CEB4"}], "total_start": 2100, "total_end": 2124, "total_duration": 24}, {"id": 27, "pc": "0x0000000004c0", "instruction": "0xb8b317fa", "disassembly": "ace_bsync x22", "stages": [{"name": "fetch", "start": 2130, "end": 2136, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 2136, "end": 2142, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2142, "end": 2148, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 2148, "end": 2154, "duration": 6, "color": "#96CEB4"}], "total_start": 2130, "total_end": 2154, "total_duration": 24}, {"id": 28, "pc": "0x0000000004c8", "instruction": "0xb748dbcf", "disassembly": "tmma.tnt t3, t25, t21", "stages": [{"name": "fetch", "start": 2169, "end": 2172, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2172, "end": 2176, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2176, "end": 2180, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2180, "end": 2191, "duration": 11, "color": "#96CEB4"}], "total_start": 2169, "total_end": 2191, "total_duration": 22}, {"id": 29, "pc": "0x0000000004d0", "instruction": "0x82d8567d", "disassembly": "tst.shared.u64 (x27), t11", "stages": [{"name": "fetch", "start": 2204, "end": 2209, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 2209, "end": 2215, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2215, "end": 2219, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2219, "end": 2226, "duration": 7, "color": "#96CEB4"}], "total_start": 2204, "total_end": 2226, "total_duration": 22}, {"id": 30, "pc": "0x0000000004d8", "instruction": "0xdeda8bbb", "disassembly": "tld.linear.u8 t6, (x3)", "stages": [{"name": "fetch", "start": 2238, "end": 2246, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 2246, "end": 2253, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2253, "end": 2256, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 2256, "end": 2283, "duration": 27, "color": "#96CEB4"}], "total_start": 2238, "total_end": 2283, "total_duration": 45}, {"id": 31, "pc": "0x0000000004e0", "instruction": "0x3a935d62", "disassembly": "ace_bsync x31", "stages": [{"name": "fetch", "start": 2291, "end": 2297, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 2297, "end": 2301, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2301, "end": 2307, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 2307, "end": 2313, "duration": 6, "color": "#96CEB4"}], "total_start": 2291, "total_end": 2313, "total_duration": 22}, {"id": 32, "pc": "0x0000000004e8", "instruction": "0x710461e3", "disassembly": "tld.global.u32 t24, (x16)", "stages": [{"name": "fetch", "start": 2320, "end": 2326, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 2326, "end": 2331, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2331, "end": 2337, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 2337, "end": 2359, "duration": 22, "color": "#96CEB4"}], "total_start": 2320, "total_end": 2359, "total_duration": 39}, {"id": 33, "pc": "0x0000000004f0", "instruction": "0xc7e99aca", "disassembly": "twait", "stages": [{"name": "fetch", "start": 2374, "end": 2378, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 2378, "end": 2382, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2382, "end": 2387, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 2387, "end": 2398, "duration": 11, "color": "#96CEB4"}], "total_start": 2374, "total_end": 2398, "total_duration": 24}, {"id": 34, "pc": "0x0000000004f8", "instruction": "0xa44528c0", "disassembly": "tld.linear.u8 t3, (x20)", "stages": [{"name": "fetch", "start": 2403, "end": 2406, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2406, "end": 2409, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2409, "end": 2416, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 2416, "end": 2436, "duration": 20, "color": "#96CEB4"}], "total_start": 2403, "total_end": 2436, "total_duration": 33}, {"id": 35, "pc": "0x000000000500", "instruction": "0xfb5cf467", "disassembly": "tld.linear.u8 t10, (x3)", "stages": [{"name": "fetch", "start": 2449, "end": 2456, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 2456, "end": 2459, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2459, "end": 2463, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2463, "end": 2470, "duration": 7, "color": "#96CEB4"}], "total_start": 2449, "total_end": 2470, "total_duration": 21}, {"id": 36, "pc": "0x000000000508", "instruction": "0x2165e210", "disassembly": "tcsrw.i 0x25", "stages": [{"name": "fetch", "start": 2484, "end": 2487, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2487, "end": 2494, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2494, "end": 2498, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2498, "end": 2521, "duration": 23, "color": "#96CEB4"}], "total_start": 2484, "total_end": 2521, "total_duration": 37}, {"id": 37, "pc": "0x000000000510", "instruction": "0x1a2c827e", "disassembly": "tmma.nnn t5, t26, t20", "stages": [{"name": "fetch", "start": 2535, "end": 2540, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 2540, "end": 2544, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2544, "end": 2552, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 2552, "end": 2567, "duration": 15, "color": "#96CEB4"}], "total_start": 2535, "total_end": 2567, "total_duration": 32}, {"id": 38, "pc": "0x000000000518", "instruction": "0x53ff5011", "disassembly": "ace_bsync x8", "stages": [{"name": "fetch", "start": 2575, "end": 2583, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 2583, "end": 2591, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2591, "end": 2596, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 2596, "end": 2615, "duration": 19, "color": "#96CEB4"}], "total_start": 2575, "total_end": 2615, "total_duration": 40}, {"id": 39, "pc": "0x000000000520", "instruction": "0xfdd4253b", "disassembly": "tmma.tnt t0, t29, t6", "stages": [{"name": "fetch", "start": 2625, "end": 2628, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2628, "end": 2635, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2635, "end": 2639, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2639, "end": 2660, "duration": 21, "color": "#96CEB4"}], "total_start": 2625, "total_end": 2660, "total_duration": 35}, {"id": 40, "pc": "0x000000000528", "instruction": "0x31e8ac68", "disassembly": "tmma.ntt t4, t15, t23", "stages": [{"name": "fetch", "start": 2669, "end": 2674, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 2674, "end": 2678, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2678, "end": 2684, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 2684, "end": 2768, "duration": 84, "color": "#96CEB4"}], "total_start": 2669, "total_end": 2768, "total_duration": 99}, {"id": 41, "pc": "0x000000000530", "instruction": "0xac96e9ec", "disassembly": "tld.linear.u8 t0, (x19)", "stages": [{"name": "fetch", "start": 2777, "end": 2785, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 2785, "end": 2788, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2788, "end": 2792, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2792, "end": 2805, "duration": 13, "color": "#96CEB4"}], "total_start": 2777, "total_end": 2805, "total_duration": 28}, {"id": 42, "pc": "0x000000000538", "instruction": "0xf3c43657", "disassembly": "tmma.tnt t9, t17, t18", "stages": [{"name": "fetch", "start": 2811, "end": 2818, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 2818, "end": 2822, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2822, "end": 2830, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 2830, "end": 2845, "duration": 15, "color": "#96CEB4"}], "total_start": 2811, "total_end": 2845, "total_duration": 34}, {"id": 43, "pc": "0x000000000540", "instruction": "0xbfffcfd2", "disassembly": "tld.texture.u16 t31, (x16)", "stages": [{"name": "fetch", "start": 2853, "end": 2856, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2856, "end": 2859, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2859, "end": 2867, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 2867, "end": 2885, "duration": 18, "color": "#96CEB4"}], "total_start": 2853, "total_end": 2885, "total_duration": 32}, {"id": 44, "pc": "0x000000000548", "instruction": "0x1b49452d", "disassembly": "tld.global.u32 t21, (x8)", "stages": [{"name": "fetch", "start": 2894, "end": 2902, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 2902, "end": 2907, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2907, "end": 2911, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 2911, "end": 2930, "duration": 19, "color": "#96CEB4"}], "total_start": 2894, "total_end": 2930, "total_duration": 36}, {"id": 45, "pc": "0x000000000550", "instruction": "0xc4a69f3c", "disassembly": "ace_bsync x0", "stages": [{"name": "fetch", "start": 2943, "end": 2946, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2946, "end": 2949, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2949, "end": 2957, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 2957, "end": 2966, "duration": 9, "color": "#96CEB4"}], "total_start": 2943, "total_end": 2966, "total_duration": 23}, {"id": 46, "pc": "0x000000000558", "instruction": "0x193923de", "disassembly": "tmma.ntt t9, t27, t8", "stages": [{"name": "fetch", "start": 2979, "end": 2982, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 2982, "end": 2987, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 2987, "end": 2992, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 2992, "end": 2998, "duration": 6, "color": "#96CEB4"}], "total_start": 2979, "total_end": 2998, "total_duration": 19}, {"id": 47, "pc": "0x000000000560", "instruction": "0x45c7936c", "disassembly": "tcsrw.i 0x6", "stages": [{"name": "fetch", "start": 3008, "end": 3013, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3013, "end": 3020, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3020, "end": 3026, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 3026, "end": 3050, "duration": 24, "color": "#96CEB4"}], "total_start": 3008, "total_end": 3050, "total_duration": 42}, {"id": 48, "pc": "0x000000000568", "instruction": "0xfcfedb99", "disassembly": "tcsrw.i 0x10", "stages": [{"name": "fetch", "start": 3057, "end": 3061, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 3061, "end": 3067, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3067, "end": 3070, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 3070, "end": 3080, "duration": 10, "color": "#96CEB4"}], "total_start": 3057, "total_end": 3080, "total_duration": 23}, {"id": 49, "pc": "0x000000000570", "instruction": "0xd84a7b28", "disassembly": "ace_bsync x15", "stages": [{"name": "fetch", "start": 3090, "end": 3095, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3095, "end": 3099, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3099, "end": 3107, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 3107, "end": 3115, "duration": 8, "color": "#96CEB4"}], "total_start": 3090, "total_end": 3115, "total_duration": 25}, {"id": 50, "pc": "0x000000000578", "instruction": "0xef465290", "disassembly": "tld.global.u32 t30, (x14)", "stages": [{"name": "fetch", "start": 3126, "end": 3130, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 3130, "end": 3136, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3136, "end": 3141, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 3141, "end": 3175, "duration": 34, "color": "#96CEB4"}], "total_start": 3126, "total_end": 3175, "total_duration": 49}, {"id": 51, "pc": "0x000000000580", "instruction": "0x49118497", "disassembly": "tld.global.u32 t12, (x25)", "stages": [{"name": "fetch", "start": 3183, "end": 3188, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3188, "end": 3193, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3193, "end": 3196, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 3196, "end": 3209, "duration": 13, "color": "#96CEB4"}], "total_start": 3183, "total_end": 3209, "total_duration": 26}, {"id": 52, "pc": "0x000000000588", "instruction": "0xb43825b5", "disassembly": "tld.linear.u8 t25, (x21)", "stages": [{"name": "fetch", "start": 3219, "end": 3222, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 3222, "end": 3225, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3225, "end": 3230, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 3230, "end": 3240, "duration": 10, "color": "#96CEB4"}], "total_start": 3219, "total_end": 3240, "total_duration": 21}, {"id": 53, "pc": "0x000000000590", "instruction": "0x53f59a85", "disassembly": "tld.global.u32 t6, (x27)", "stages": [{"name": "fetch", "start": 3254, "end": 3259, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3259, "end": 3267, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3267, "end": 3272, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 3272, "end": 3290, "duration": 18, "color": "#96CEB4"}], "total_start": 3254, "total_end": 3290, "total_duration": 36}, {"id": 54, "pc": "0x000000000598", "instruction": "0x92ec9f2d", "disassembly": "tmma.tnt t24, t12, t16", "stages": [{"name": "fetch", "start": 3304, "end": 3307, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 3307, "end": 3315, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3315, "end": 3321, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 3321, "end": 3326, "duration": 5, "color": "#96CEB4"}], "total_start": 3304, "total_end": 3326, "total_duration": 22}, {"id": 55, "pc": "0x0000000005a0", "instruction": "0xfcf27e76", "disassembly": "tld.linear.u8 t12, (x23)", "stages": [{"name": "fetch", "start": 3339, "end": 3345, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 3345, "end": 3348, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3348, "end": 3356, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 3356, "end": 3371, "duration": 15, "color": "#96CEB4"}], "total_start": 3339, "total_end": 3371, "total_duration": 32}, {"id": 56, "pc": "0x0000000005a8", "instruction": "0x605cc686", "disassembly": "tmma.tnt t19, t19, t26", "stages": [{"name": "fetch", "start": 3385, "end": 3390, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3390, "end": 3396, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3396, "end": 3404, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 3404, "end": 3418, "duration": 14, "color": "#96CEB4"}], "total_start": 3385, "total_end": 3418, "total_duration": 33}, {"id": 57, "pc": "0x0000000005b0", "instruction": "0x3095eef6", "disassembly": "tcsrw.i 0x26", "stages": [{"name": "fetch", "start": 3431, "end": 3439, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 3439, "end": 3445, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3445, "end": 3453, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 3453, "end": 3463, "duration": 10, "color": "#96CEB4"}], "total_start": 3431, "total_end": 3463, "total_duration": 32}, {"id": 58, "pc": "0x0000000005b8", "instruction": "0xa1b0e1d9", "disassembly": "tld.texture.u16 t25, (x0)", "stages": [{"name": "fetch", "start": 3477, "end": 3482, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3482, "end": 3487, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3487, "end": 3491, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 3491, "end": 3509, "duration": 18, "color": "#96CEB4"}], "total_start": 3477, "total_end": 3509, "total_duration": 32}, {"id": 59, "pc": "0x0000000005c0", "instruction": "0xab4e2c24", "disassembly": "tmma.ntt t29, t28, t28", "stages": [{"name": "fetch", "start": 3523, "end": 3531, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 3531, "end": 3535, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3535, "end": 3542, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 3542, "end": 3562, "duration": 20, "color": "#96CEB4"}], "total_start": 3523, "total_end": 3562, "total_duration": 39}, {"id": 60, "pc": "0x0000000005c8", "instruction": "0xb8aa7158", "disassembly": "tmma.tnt t18, t21, t5", "stages": [{"name": "fetch", "start": 3569, "end": 3573, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 3573, "end": 3581, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3581, "end": 3586, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 3586, "end": 3650, "duration": 64, "color": "#96CEB4"}], "total_start": 3569, "total_end": 3650, "total_duration": 81}, {"id": 61, "pc": "0x0000000005d0", "instruction": "0x35b8fd4b", "disassembly": "tld.global.u32 t2, (x15)", "stages": [{"name": "fetch", "start": 3658, "end": 3664, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 3664, "end": 3671, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3671, "end": 3674, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 3674, "end": 3693, "duration": 19, "color": "#96CEB4"}], "total_start": 3658, "total_end": 3693, "total_duration": 35}, {"id": 62, "pc": "0x0000000005d8", "instruction": "0xf2d9de5d", "disassembly": "tmma.nnn t12, t24, t31", "stages": [{"name": "fetch", "start": 3704, "end": 3710, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 3710, "end": 3714, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3714, "end": 3718, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 3718, "end": 3743, "duration": 25, "color": "#96CEB4"}], "total_start": 3704, "total_end": 3743, "total_duration": 39}, {"id": 63, "pc": "0x0000000005e0", "instruction": "0xf4855aa1", "disassembly": "tmma.tnt t27, t14, t11", "stages": [{"name": "fetch", "start": 3748, "end": 3756, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 3756, "end": 3763, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3763, "end": 3769, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 3769, "end": 3775, "duration": 6, "color": "#96CEB4"}], "total_start": 3748, "total_end": 3775, "total_duration": 27}, {"id": 64, "pc": "0x0000000005e8", "instruction": "0x4fcb7546", "disassembly": "tmma.tnt t29, t8, t29", "stages": [{"name": "fetch", "start": 3788, "end": 3796, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 3796, "end": 3803, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3803, "end": 3810, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 3810, "end": 3834, "duration": 24, "color": "#96CEB4"}], "total_start": 3788, "total_end": 3834, "total_duration": 46}, {"id": 65, "pc": "0x0000000005f0", "instruction": "0xd1581092", "disassembly": "twait", "stages": [{"name": "fetch", "start": 3844, "end": 3851, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 3851, "end": 3859, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3859, "end": 3866, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 3866, "end": 3884, "duration": 18, "color": "#96CEB4"}], "total_start": 3844, "total_end": 3884, "total_duration": 40}, {"id": 66, "pc": "0x0000000005f8", "instruction": "0x822764e6", "disassembly": "tst.shared.u64 (x30), t28", "stages": [{"name": "fetch", "start": 3897, "end": 3902, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 3902, "end": 3906, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3906, "end": 3914, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 3914, "end": 3927, "duration": 13, "color": "#96CEB4"}], "total_start": 3897, "total_end": 3927, "total_duration": 30}, {"id": 67, "pc": "0x000000000600", "instruction": "0x8c0e8cd8", "disassembly": "tcsrw.i 0x17", "stages": [{"name": "fetch", "start": 3940, "end": 3946, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 3946, "end": 3949, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3949, "end": 3957, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 3957, "end": 3971, "duration": 14, "color": "#96CEB4"}], "total_start": 3940, "total_end": 3971, "total_duration": 31}, {"id": 68, "pc": "0x000000000608", "instruction": "0x558f1f19", "disassembly": "tmma.ntt t20, t5, t8", "stages": [{"name": "fetch", "start": 3979, "end": 3983, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 3983, "end": 3987, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 3987, "end": 3993, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 3993, "end": 4002, "duration": 9, "color": "#96CEB4"}], "total_start": 3979, "total_end": 4002, "total_duration": 23}, {"id": 69, "pc": "0x000000000610", "instruction": "0x20714d51", "disassembly": "ace_bsync x26", "stages": [{"name": "fetch", "start": 4010, "end": 4015, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 4015, "end": 4022, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4022, "end": 4028, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 4028, "end": 4046, "duration": 18, "color": "#96CEB4"}], "total_start": 4010, "total_end": 4046, "total_duration": 36}, {"id": 70, "pc": "0x000000000618", "instruction": "0x44f3193c", "disassembly": "ace_bsync x24", "stages": [{"name": "fetch", "start": 4051, "end": 4058, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 4058, "end": 4066, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4066, "end": 4069, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 4069, "end": 4118, "duration": 49, "color": "#96CEB4"}], "total_start": 4051, "total_end": 4118, "total_duration": 67}, {"id": 71, "pc": "0x000000000620", "instruction": "0x7160a6b4", "disassembly": "twait", "stages": [{"name": "fetch", "start": 4132, "end": 4135, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 4135, "end": 4140, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4140, "end": 4145, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 4145, "end": 4162, "duration": 17, "color": "#96CEB4"}], "total_start": 4132, "total_end": 4162, "total_duration": 30}, {"id": 72, "pc": "0x000000000628", "instruction": "0x99c8d2ab", "disassembly": "tld.linear.u8 t14, (x31)", "stages": [{"name": "fetch", "start": 4173, "end": 4177, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 4177, "end": 4182, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4182, "end": 4188, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 4188, "end": 4208, "duration": 20, "color": "#96CEB4"}], "total_start": 4173, "total_end": 4208, "total_duration": 35}, {"id": 73, "pc": "0x000000000630", "instruction": "0x738c254c", "disassembly": "tmma.ntt t25, t10, t29", "stages": [{"name": "fetch", "start": 4213, "end": 4217, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 4217, "end": 4224, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4224, "end": 4231, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 4231, "end": 4236, "duration": 5, "color": "#96CEB4"}], "total_start": 4213, "total_end": 4236, "total_duration": 23}, {"id": 74, "pc": "0x000000000638", "instruction": "0xa78648f8", "disassembly": "tmma.nnn t1, t5, t27", "stages": [{"name": "fetch", "start": 4247, "end": 4251, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 4251, "end": 4257, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4257, "end": 4261, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 4261, "end": 4267, "duration": 6, "color": "#96CEB4"}], "total_start": 4247, "total_end": 4267, "total_duration": 20}, {"id": 75, "pc": "0x000000000640", "instruction": "0x710cf373", "disassembly": "tmma.ntt t13, t29, t20", "stages": [{"name": "fetch", "start": 4276, "end": 4281, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 4281, "end": 4287, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4287, "end": 4292, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 4292, "end": 4310, "duration": 18, "color": "#96CEB4"}], "total_start": 4276, "total_end": 4310, "total_duration": 34}, {"id": 76, "pc": "0x000000000648", "instruction": "0xe5bcb8d0", "disassembly": "tmma.tnt t30, t1, t3", "stages": [{"name": "fetch", "start": 4319, "end": 4324, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 4324, "end": 4328, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4328, "end": 4336, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 4336, "end": 4343, "duration": 7, "color": "#96CEB4"}], "total_start": 4319, "total_end": 4343, "total_duration": 24}, {"id": 77, "pc": "0x000000000650", "instruction": "0x1a4e5b70", "disassembly": "tld.global.u32 t15, (x12)", "stages": [{"name": "fetch", "start": 4358, "end": 4361, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 4361, "end": 4368, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4368, "end": 4372, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 4372, "end": 4384, "duration": 12, "color": "#96CEB4"}], "total_start": 4358, "total_end": 4384, "total_duration": 26}, {"id": 78, "pc": "0x000000000658", "instruction": "0x893b4c32", "disassembly": "tmma.tnt t13, t29, t16", "stages": [{"name": "fetch", "start": 4391, "end": 4396, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 4396, "end": 4400, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4400, "end": 4407, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 4407, "end": 4431, "duration": 24, "color": "#96CEB4"}], "total_start": 4391, "total_end": 4431, "total_duration": 40}, {"id": 79, "pc": "0x000000000660", "instruction": "0xd71d5e60", "disassembly": "tst.shared.u64 (x19), t6", "stages": [{"name": "fetch", "start": 4437, "end": 4444, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 4444, "end": 4447, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4447, "end": 4452, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 4452, "end": 4475, "duration": 23, "color": "#96CEB4"}], "total_start": 4437, "total_end": 4475, "total_duration": 38}, {"id": 80, "pc": "0x000000000668", "instruction": "0xf87466d7", "disassembly": "ace_bsync x25", "stages": [{"name": "fetch", "start": 4490, "end": 4498, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 4498, "end": 4502, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4502, "end": 4505, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 4505, "end": 4592, "duration": 87, "color": "#96CEB4"}], "total_start": 4490, "total_end": 4592, "total_duration": 102}, {"id": 81, "pc": "0x000000000670", "instruction": "0x4e2b6091", "disassembly": "tmma.tnt t19, t7, t2", "stages": [{"name": "fetch", "start": 4607, "end": 4612, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 4612, "end": 4619, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4619, "end": 4625, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 4625, "end": 4641, "duration": 16, "color": "#96CEB4"}], "total_start": 4607, "total_end": 4641, "total_duration": 34}, {"id": 82, "pc": "0x000000000678", "instruction": "0x9186a576", "disassembly": "tmma.ntt t0, t26, t31", "stages": [{"name": "fetch", "start": 4647, "end": 4650, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 4650, "end": 4656, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4656, "end": 4661, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 4661, "end": 4686, "duration": 25, "color": "#96CEB4"}], "total_start": 4647, "total_end": 4686, "total_duration": 39}, {"id": 83, "pc": "0x000000000680", "instruction": "0xc5122df8", "disassembly": "tst.shared.u64 (x27), t11", "stages": [{"name": "fetch", "start": 4698, "end": 4706, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 4706, "end": 4713, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4713, "end": 4721, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 4721, "end": 4734, "duration": 13, "color": "#96CEB4"}], "total_start": 4698, "total_end": 4734, "total_duration": 36}, {"id": 84, "pc": "0x000000000688", "instruction": "0xdee624d0", "disassembly": "tld.linear.u8 t30, (x29)", "stages": [{"name": "fetch", "start": 4748, "end": 4754, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 4754, "end": 4762, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4762, "end": 4769, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 4769, "end": 4782, "duration": 13, "color": "#96CEB4"}], "total_start": 4748, "total_end": 4782, "total_duration": 34}, {"id": 85, "pc": "0x000000000690", "instruction": "0xea09dfa0", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 4792, "end": 4797, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 4797, "end": 4803, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4803, "end": 4807, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 4807, "end": 4826, "duration": 19, "color": "#96CEB4"}], "total_start": 4792, "total_end": 4826, "total_duration": 34}, {"id": 86, "pc": "0x000000000698", "instruction": "0xac3eb2d5", "disassembly": "ace_bsync x21", "stages": [{"name": "fetch", "start": 4840, "end": 4843, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 4843, "end": 4849, "duration": 6, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4849, "end": 4854, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 4854, "end": 4864, "duration": 10, "color": "#96CEB4"}], "total_start": 4840, "total_end": 4864, "total_duration": 24}, {"id": 87, "pc": "0x0000000006a0", "instruction": "0x464d7c87", "disassembly": "tmma.ntt t16, t21, t17", "stages": [{"name": "fetch", "start": 4876, "end": 4883, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 4883, "end": 4891, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4891, "end": 4896, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 4896, "end": 4918, "duration": 22, "color": "#96CEB4"}], "total_start": 4876, "total_end": 4918, "total_duration": 42}, {"id": 88, "pc": "0x0000000006a8", "instruction": "0x9441aefd", "disassembly": "tcsrw.i 0x5", "stages": [{"name": "fetch", "start": 4923, "end": 4927, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 4927, "end": 4935, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4935, "end": 4941, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 4941, "end": 4961, "duration": 20, "color": "#96CEB4"}], "total_start": 4923, "total_end": 4961, "total_duration": 38}, {"id": 89, "pc": "0x0000000006b0", "instruction": "0xd2171429", "disassembly": "tcsrw.i 0x30", "stages": [{"name": "fetch", "start": 4974, "end": 4982, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 4982, "end": 4990, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 4990, "end": 4996, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 4996, "end": 5015, "duration": 19, "color": "#96CEB4"}], "total_start": 4974, "total_end": 5015, "total_duration": 41}, {"id": 90, "pc": "0x0000000006b8", "instruction": "0x27d2582e", "disassembly": "tld.texture.u16 t14, (x25)", "stages": [{"name": "fetch", "start": 5020, "end": 5028, "duration": 8, "color": "#FF6B6B"}, {"name": "decode", "start": 5028, "end": 5032, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5032, "end": 5037, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 5037, "end": 5083, "duration": 46, "color": "#96CEB4"}], "total_start": 5020, "total_end": 5083, "total_duration": 63}, {"id": 91, "pc": "0x0000000006c0", "instruction": "0x6e781fd7", "disassembly": "twait", "stages": [{"name": "fetch", "start": 5097, "end": 5104, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 5104, "end": 5111, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5111, "end": 5116, "duration": 5, "color": "#45B7D1"}, {"name": "execute", "start": 5116, "end": 5134, "duration": 18, "color": "#96CEB4"}], "total_start": 5097, "total_end": 5134, "total_duration": 37}, {"id": 92, "pc": "0x0000000006c8", "instruction": "0x64aebd1b", "disassembly": "tmma.ntt t29, t17, t19", "stages": [{"name": "fetch", "start": 5147, "end": 5152, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 5152, "end": 5156, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5156, "end": 5159, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 5159, "end": 5170, "duration": 11, "color": "#96CEB4"}], "total_start": 5147, "total_end": 5170, "total_duration": 23}, {"id": 93, "pc": "0x0000000006d0", "instruction": "0x2e9b23bc", "disassembly": "tld.linear.u8 t11, (x12)", "stages": [{"name": "fetch", "start": 5180, "end": 5184, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 5184, "end": 5192, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5192, "end": 5198, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 5198, "end": 5211, "duration": 13, "color": "#96CEB4"}], "total_start": 5180, "total_end": 5211, "total_duration": 31}, {"id": 94, "pc": "0x0000000006d8", "instruction": "0xd29cfc0c", "disassembly": "tld.linear.u8 t18, (x6)", "stages": [{"name": "fetch", "start": 5225, "end": 5229, "duration": 4, "color": "#FF6B6B"}, {"name": "decode", "start": 5229, "end": 5234, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5234, "end": 5238, "duration": 4, "color": "#45B7D1"}, {"name": "execute", "start": 5238, "end": 5254, "duration": 16, "color": "#96CEB4"}], "total_start": 5225, "total_end": 5254, "total_duration": 29}, {"id": 95, "pc": "0x0000000006e0", "instruction": "0x5d6168bd", "disassembly": "tld.global.u32 t8, (x17)", "stages": [{"name": "fetch", "start": 5261, "end": 5264, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 5264, "end": 5267, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5267, "end": 5274, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 5274, "end": 5288, "duration": 14, "color": "#96CEB4"}], "total_start": 5261, "total_end": 5288, "total_duration": 27}, {"id": 96, "pc": "0x0000000006e8", "instruction": "0xb34b6cf6", "disassembly": "twait", "stages": [{"name": "fetch", "start": 5295, "end": 5298, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 5298, "end": 5301, "duration": 3, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5301, "end": 5308, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 5308, "end": 5322, "duration": 14, "color": "#96CEB4"}], "total_start": 5295, "total_end": 5322, "total_duration": 27}, {"id": 97, "pc": "0x0000000006f0", "instruction": "0x8a8d03aa", "disassembly": "twait", "stages": [{"name": "fetch", "start": 5334, "end": 5339, "duration": 5, "color": "#FF6B6B"}, {"name": "decode", "start": 5339, "end": 5343, "duration": 4, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5343, "end": 5346, "duration": 3, "color": "#45B7D1"}, {"name": "execute", "start": 5346, "end": 5359, "duration": 13, "color": "#96CEB4"}], "total_start": 5334, "total_end": 5359, "total_duration": 25}, {"id": 98, "pc": "0x0000000006f8", "instruction": "0x2d34d08e", "disassembly": "tmma.tnt t25, t31, t4", "stages": [{"name": "fetch", "start": 5371, "end": 5378, "duration": 7, "color": "#FF6B6B"}, {"name": "decode", "start": 5378, "end": 5386, "duration": 8, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5386, "end": 5394, "duration": 8, "color": "#45B7D1"}, {"name": "execute", "start": 5394, "end": 5400, "duration": 6, "color": "#96CEB4"}], "total_start": 5371, "total_end": 5400, "total_duration": 29}, {"id": 99, "pc": "0x000000000700", "instruction": "0x3631d00b", "disassembly": "tmma.nnn t19, t5, t15", "stages": [{"name": "fetch", "start": 5407, "end": 5410, "duration": 3, "color": "#FF6B6B"}, {"name": "decode", "start": 5410, "end": 5417, "duration": 7, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5417, "end": 5423, "duration": 6, "color": "#45B7D1"}, {"name": "execute", "start": 5423, "end": 5447, "duration": 24, "color": "#96CEB4"}], "total_start": 5407, "total_end": 5447, "total_duration": 40}, {"id": 100, "pc": "0x000000000708", "instruction": "0xda6dfda1", "disassembly": "tmma.nnn t14, t24, t28", "stages": [{"name": "fetch", "start": 5461, "end": 5467, "duration": 6, "color": "#FF6B6B"}, {"name": "decode", "start": 5467, "end": 5472, "duration": 5, "color": "#4ECDC4"}, {"name": "dispatch", "start": 5472, "end": 5479, "duration": 7, "color": "#45B7D1"}, {"name": "execute", "start": 5479, "end": 5556, "duration": 77, "color": "#96CEB4"}], "total_start": 5461, "total_end": 5556, "total_duration": 95}];
        const minTime = 1000;
        const maxTime = 5556;
        const timeRange = maxTime - minTime;

        let currentZoom = 1.0;
        let currentFilter = '';
        let currentSort = 'id';
        let currentScrollLeft = 0;

        // 拖拽缩放相关变量
        let isDragging = false;
        let dragStartY = 0;
        let dragStartZoom = 1.0;

        // 初始化时间轴
        function initTimeAxis() {
            const timeAxis = document.getElementById('timeAxis');
            timeAxis.innerHTML = ''; // 清空现有内容

            // 计算时间轴的实际宽度（像素）
            const baseWidth = 800;
            const actualWidth = baseWidth * currentZoom;

            // 根据缩放级别调整刻度数量，确保刻度密度合适
            const baseTickCount = 10;
            // 限制最大刻度数量，避免在极高缩放下性能问题
            const maxTickCount = 200;
            const tickCount = Math.min(maxTickCount, Math.max(baseTickCount, Math.floor(baseTickCount * currentZoom)));

            for (let i = 0; i <= tickCount; i++) {
                const time = minTime + (timeRange * i / tickCount);
                // 使用像素位置而不是百分比
                const positionPx = (i / tickCount) * actualWidth;

                const tick = document.createElement('div');
                tick.className = 'time-tick';
                tick.style.left = positionPx + 'px';
                timeAxis.appendChild(tick);

                const label = document.createElement('div');
                label.className = 'time-label';
                label.style.left = positionPx + 'px';
                label.textContent = Math.round(time);
                timeAxis.appendChild(label);
            }
        }

        // 计算时间条的位置和宽度
        function calculateBarPosition(start, end) {
            const baseWidth = 800;
            const actualWidth = baseWidth * currentZoom;

            // 计算像素位置
            const startPx = ((start - minTime) / timeRange) * actualWidth;
            const widthPx = ((end - start) / timeRange) * actualWidth;

            return {
                left: startPx + 'px',
                width: Math.max(widthPx, 2) + 'px'  // 最小宽度2px
            };
        }

        // 显示工具提示
        function showTooltip(event, stage, instruction) {
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${stage.name.toUpperCase()}</strong><br>
                指令: ${instruction.disassembly}<br>
                开始: ${stage.start}<br>
                结束: ${stage.end}<br>
                持续: ${stage.duration}
            `;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.style.opacity = '1';
        }

        // 隐藏工具提示
        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }

        // 渲染指令列表
        function renderInstructions(filteredInstructions = null) {
            const instructionList = document.getElementById('instructionList');
            const leftPanelContent = document.getElementById('leftPanelContent');
            const instructionsToRender = filteredInstructions || instructions;

            instructionList.innerHTML = '';
            leftPanelContent.innerHTML = '';

            instructionsToRender.forEach((instruction, index) => {
                // 创建左侧固定信息
                const leftInfo = document.createElement('div');
                leftInfo.className = 'instruction-left';
                leftInfo.innerHTML = `
                    <div class="instruction-pc">${instruction.pc}</div>
                    <div class="instruction-disasm">${instruction.disassembly}</div>
                `;

                // 添加点击事件
                leftInfo.addEventListener('click', () => {
                    handleInstructionClick(instruction);
                });

                // 添加标题提示
                leftInfo.title = `点击居中显示指令 ID: ${instruction.id}`;

                leftPanelContent.appendChild(leftInfo);

                // 创建右侧时间线
                const rightPanel = document.createElement('div');
                rightPanel.className = 'instruction-right';

                const timeline = document.createElement('div');
                timeline.className = 'timeline-bars';
                timeline.style.width = (800 * currentZoom) + 'px';

                instruction.stages.forEach(stage => {
                    const bar = document.createElement('div');
                    bar.className = `stage-bar ${stage.name}`;
                    bar.style.backgroundColor = stage.color;

                    const position = calculateBarPosition(stage.start, stage.end);
                    bar.style.left = position.left;
                    bar.style.width = position.width;

                    // 检查宽度是否足够显示文本（从像素字符串中提取数值）
                    const widthPx = parseFloat(position.width);
                    if (widthPx > 20) {
                        bar.textContent = stage.name;
                    }

                    bar.addEventListener('mouseenter', (e) => showTooltip(e, stage, instruction));
                    bar.addEventListener('mouseleave', hideTooltip);

                    timeline.appendChild(bar);
                });

                rightPanel.appendChild(timeline);
                instructionList.appendChild(rightPanel);
            });

            // 调整高度匹配
            adjustHeights();
        }

        // 调整左右面板高度匹配
        function adjustHeights() {
            const leftPanels = document.querySelectorAll('.instruction-left');
            const rightPanels = document.querySelectorAll('.instruction-right');

            for (let i = 0; i < Math.min(leftPanels.length, rightPanels.length); i++) {
                const leftPanel = leftPanels[i];
                const rightPanel = rightPanels[i];

                // 重置高度以获取自然高度
                leftPanel.style.height = 'auto';
                rightPanel.style.height = 'auto';

                // 等待DOM更新
                setTimeout(() => {
                    const leftHeight = leftPanel.offsetHeight;
                    const rightHeight = rightPanel.offsetHeight;
                    const maxHeight = Math.max(leftHeight, rightHeight);

                    // 设置相同的高度
                    leftPanel.style.height = maxHeight + 'px';
                    rightPanel.style.height = maxHeight + 'px';
                }, 0);
            }
        }

        // 过滤指令
        function filterInstructions() {
            const filter = document.getElementById('filterInput').value.toLowerCase();
            if (!filter) {
                renderInstructions();
                return;
            }

            const filtered = instructions.filter(instr =>
                instr.pc.toLowerCase().includes(filter) ||
                instr.disassembly.toLowerCase().includes(filter)
            );

            renderInstructions(filtered);
        }

        // 排序指令
        function sortInstructions() {
            const sortBy = document.getElementById('sortSelect').value;
            const sorted = [...instructions];

            sorted.sort((a, b) => {
                switch(sortBy) {
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }
            });

            renderInstructions(sorted);
        }

        // 缩放功能
        function updateZoom() {
            const zoom = parseFloat(document.getElementById('zoomSlider').value);
            currentZoom = zoom;
            document.getElementById('zoomValue').textContent = zoom.toFixed(1) + 'x';

            // 应用缩放
            applyZoom();
        }

        // 重置视图
        function resetView() {
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('filterInput').value = '';
            document.getElementById('sortSelect').value = 'id';
            currentScrollLeft = 0;
            updateZoom();
            renderInstructions();
            // 重置滚动位置
            const timelineContainer = document.querySelector('.timeline-container');
            timelineContainer.scrollLeft = 0;
        }

        // 拖拽缩放功能
        function initDragZoom() {
            const scrollablePanel = document.querySelector('.scrollable-right-panel');

            // 鼠标按下事件
            scrollablePanel.addEventListener('mousedown', function(event) {
                // 只响应左键
                if (event.button === 0) {
                    isDragging = true;
                    dragStartY = event.clientY;
                    dragStartZoom = currentZoom;

                    // 改变鼠标样式
                    scrollablePanel.style.cursor = 'ns-resize';

                    // 阻止默认行为
                    event.preventDefault();
                }
            });

            // 鼠标移动事件
            document.addEventListener('mousemove', function(event) {
                if (isDragging) {
                    const deltaY = dragStartY - event.clientY; // 上拖为正，下拖为负
                    const sensitivity = 0.01; // 缩放敏感度
                    const zoomDelta = deltaY * sensitivity;

                    let newZoom = dragStartZoom + zoomDelta;
                    newZoom = Math.max(0.1, Math.min(100, newZoom)); // 限制缩放范围

                    // 更新缩放滑块和应用缩放
                    const zoomSlider = document.getElementById('zoomSlider');
                    zoomSlider.value = newZoom;
                    currentZoom = newZoom;
                    document.getElementById('zoomValue').textContent = newZoom.toFixed(1) + 'x';

                    // 应用缩放
                    applyZoom();

                    event.preventDefault();
                }
            });

            // 鼠标释放事件
            document.addEventListener('mouseup', function(event) {
                if (isDragging) {
                    isDragging = false;

                    // 恢复鼠标样式
                    scrollablePanel.style.cursor = '';
                }
            });

            // 鼠标离开窗口时停止拖拽
            document.addEventListener('mouseleave', function(event) {
                if (isDragging) {
                    isDragging = false;
                    scrollablePanel.style.cursor = '';
                }
            });
        }

        // 应用缩放（从updateZoom中提取出来的核心逻辑）
        function applyZoom() {
            // 更新时间轴的宽度
            const baseWidth = 800;
            const newWidth = (baseWidth * currentZoom) + 'px';

            const timeAxis = document.getElementById('timeAxis');
            timeAxis.style.width = newWidth;
            timeAxis.style.minWidth = newWidth;

            // 重新渲染时间轴和指令以应用缩放
            initTimeAxis();
            renderInstructions();
        }

        // 居中显示指定时间
        function centerOnTime(targetTime) {
            const scrollablePanel = document.querySelector('.scrollable-right-panel');
            if (!scrollablePanel) return;

            // 计算目标时间在时间轴上的像素位置
            const baseWidth = 800;
            const actualWidth = baseWidth * currentZoom;
            const timePosition = ((targetTime - minTime) / timeRange) * actualWidth;

            // 计算滚动位置，使目标时间居中
            const panelWidth = scrollablePanel.clientWidth;
            const scrollPosition = timePosition - (panelWidth / 2);

            // 限制滚动位置在有效范围内
            const maxScroll = scrollablePanel.scrollWidth - panelWidth;
            const finalScrollPosition = Math.max(0, Math.min(scrollPosition, maxScroll));

            // 平滑滚动到目标位置
            scrollablePanel.scrollTo({
                left: finalScrollPosition,
                behavior: 'smooth'
            });
        }

        // 点击指令名居中显示
        function handleInstructionClick(instruction) {
            // 找到指令的开始时间
            let startTime = instruction.total_start;

            // 如果没有总开始时间，使用第一个阶段的开始时间
            if (!startTime && instruction.stages && instruction.stages.length > 0) {
                startTime = Math.min(...instruction.stages.map(stage => stage.start));
            }

            if (startTime) {
                centerOnTime(startTime);

                // 可选：高亮显示对应的时间条（添加临时高亮效果）
                highlightInstruction(instruction.id);
            }
        }

        // 高亮指令（临时视觉反馈）
        function highlightInstruction(instructionId) {
            // 移除之前的高亮
            document.querySelectorAll('.instruction-left, .instruction-right').forEach(element => {
                element.classList.remove('highlighted');
            });

            // 添加高亮样式
            const instructionRows = document.querySelectorAll('.instruction-right');
            const leftPanels = document.querySelectorAll('.instruction-left');

            // 获取当前显示的指令列表（可能是过滤后的）
            const currentInstructions = getCurrentDisplayedInstructions();

            // 找到对应的指令行并高亮
            for (let i = 0; i < currentInstructions.length; i++) {
                const instruction = currentInstructions[i];
                if (instruction && instruction.id === instructionId) {
                    if (instructionRows[i]) {
                        instructionRows[i].classList.add('highlighted');
                    }
                    if (leftPanels[i]) {
                        leftPanels[i].classList.add('highlighted');
                    }

                    // 3秒后移除高亮
                    setTimeout(() => {
                        if (instructionRows[i]) {
                            instructionRows[i].classList.remove('highlighted');
                        }
                        if (leftPanels[i]) {
                            leftPanels[i].classList.remove('highlighted');
                        }
                    }, 3000);
                    break;
                }
            }
        }

        // 获取当前显示的指令列表
        function getCurrentDisplayedInstructions() {
            const filter = document.getElementById('filterInput').value.toLowerCase();
            const sortBy = document.getElementById('sortSelect').value;

            let currentInstructions = [...instructions];

            // 应用过滤
            if (filter) {
                currentInstructions = currentInstructions.filter(instr =>
                    instr.pc.toLowerCase().includes(filter) ||
                    instr.disassembly.toLowerCase().includes(filter)
                );
            }

            // 应用排序
            currentInstructions.sort((a, b) => {
                switch(sortBy) {
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }
            });

            return currentInstructions;
        }

        // 跳转到指定指令并自动调整缩放和居中
        function jumpToInstruction(instructionId) {
            // 找到目标指令
            const targetInstruction = instructions.find(instr => instr.id === instructionId);
            if (!targetInstruction) {
                console.warn('未找到指令 ID:', instructionId);
                return;
            }

            // 计算指令的时间范围
            let instrStartTime = targetInstruction.total_start;
            let instrEndTime = targetInstruction.total_end;
            let instrDuration = targetInstruction.total_duration;

            // 如果没有总时间，使用阶段时间
            if (!instrStartTime && targetInstruction.stages && targetInstruction.stages.length > 0) {
                const stageTimes = targetInstruction.stages.map(stage => [stage.start, stage.end]).flat();
                instrStartTime = Math.min(...stageTimes);
                instrEndTime = Math.max(...stageTimes);
                instrDuration = instrEndTime - instrStartTime;
            }

            if (!instrStartTime || instrDuration <= 0) {
                console.warn('指令时间数据无效:', targetInstruction);
                return;
            }

            // 计算合适的缩放级别，使指令占据屏幕宽度的30-50%
            const scrollablePanel = document.querySelector('.scrollable-right-panel');
            const panelWidth = scrollablePanel.clientWidth;
            const targetWidthRatio = 0.4; // 目标宽度比例
            const targetPixelWidth = panelWidth * targetWidthRatio;

            // 计算需要的缩放级别
            const baseWidth = 800;
            const currentInstrPixelWidth = (instrDuration / timeRange) * baseWidth;
            const requiredZoom = targetPixelWidth / currentInstrPixelWidth;

            // 限制缩放范围
            const finalZoom = Math.max(0.1, Math.min(100, requiredZoom));

            // 应用缩放
            const zoomSlider = document.getElementById('zoomSlider');
            zoomSlider.value = finalZoom;
            currentZoom = finalZoom;
            document.getElementById('zoomValue').textContent = finalZoom.toFixed(1) + 'x';

            // 应用缩放并等待渲染完成
            applyZoom();

            // 延迟执行居中，确保缩放已经应用
            setTimeout(() => {
                // 计算指令中心时间
                const centerTime = instrStartTime + (instrDuration / 2);
                centerOnTime(centerTime);

                // 垂直滚动到目标指令
                scrollToInstruction(instructionId);

                // 高亮显示指令
                highlightInstruction(instructionId);

                console.log(`已跳转到指令 ID: ${instructionId}, 时间: ${instrStartTime}-${instrEndTime}, 缩放: ${finalZoom.toFixed(1)}x`);
            }, 100);
        }

        // 垂直滚动到指定指令
        function scrollToInstruction(instructionId) {
            // 获取当前显示的指令列表
            const currentInstructions = getCurrentDisplayedInstructions();

            // 找到目标指令在当前显示列表中的索引
            const targetIndex = currentInstructions.findIndex(instr => instr.id === instructionId);

            if (targetIndex === -1) {
                console.warn('在当前显示的指令列表中未找到指令 ID:', instructionId);
                return;
            }

            // 获取左侧面板的指令元素
            const leftPanels = document.querySelectorAll('.instruction-left');

            if (targetIndex < leftPanels.length) {
                const targetElement = leftPanels[targetIndex];

                // 滚动整个网页到目标指令位置
                const targetRect = targetElement.getBoundingClientRect();
                const windowHeight = window.innerHeight;

                // 计算目标元素相对于文档顶部的位置
                const targetOffsetTop = targetRect.top + window.pageYOffset;

                // 计算滚动位置，使目标指令在视窗中央
                const scrollPosition = targetOffsetTop - (windowHeight / 2) + (targetRect.height / 2);

                // 平滑滚动整个网页到目标位置
                window.scrollTo({
                    top: Math.max(0, scrollPosition),
                    behavior: 'smooth'
                });

                console.log(`网页垂直滚动到指令 ID: ${instructionId}, 索引: ${targetIndex}, 位置: ${scrollPosition}`);
            } else {
                console.warn('指令索引超出范围:', targetIndex, '总数:', leftPanels.length);
            }
        }

        // 键盘快捷键处理
        function handleKeyPress(event) {
            // 如果用户正在输入框中输入，不处理快捷键
            if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') {
                return;
            }

            const zoomSlider = document.getElementById('zoomSlider');
            const timelineContainer = document.querySelector('.timeline-container');
            const scrollStep = 50; // 滚动步长
            const zoomStep = 0.2; // 缩放步长

            switch(event.key.toLowerCase()) {
                case 'w': // 放大
                    event.preventDefault();
                    const newZoomUp = Math.min(parseFloat(zoomSlider.value) + zoomStep, 100);
                    zoomSlider.value = newZoomUp;
                    updateZoom();
                    break;

                case 's': // 缩小
                    event.preventDefault();
                    const newZoomDown = Math.max(parseFloat(zoomSlider.value) - zoomStep, 0.1);
                    zoomSlider.value = newZoomDown;
                    updateZoom();
                    break;

                case 'a': // 左移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.max(timelineContainer.scrollLeft - scrollStep, 0);
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'd': // 右移
                    event.preventDefault();
                    timelineContainer.scrollLeft = Math.min(
                        timelineContainer.scrollLeft + scrollStep,
                        timelineContainer.scrollWidth - timelineContainer.clientWidth
                    );
                    currentScrollLeft = timelineContainer.scrollLeft;
                    break;

                case 'r': // 复位缩放
                    event.preventDefault();
                    zoomSlider.value = 1;
                    updateZoom();
                    timelineContainer.scrollLeft = 0;
                    currentScrollLeft = 0;
                    break;
            }
        }

        // 事件监听器
        document.getElementById('zoomSlider').addEventListener('input', updateZoom);
        document.getElementById('filterInput').addEventListener('input', filterInstructions);
        document.getElementById('sortSelect').addEventListener('change', sortInstructions);

        // 键盘事件监听器
        document.addEventListener('keydown', handleKeyPress);

        // 初始化
        initTimeAxis();
        renderInstructions();
        initDragZoom();
    </script>
</body>
</html>
        
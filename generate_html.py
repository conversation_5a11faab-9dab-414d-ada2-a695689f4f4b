#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from itrace_timeline_visualizer import ITraceTimelineVisualizer, create_test_data

def main():
    print("正在生成大型测试数据...")

    # 创建大型测试数据
    test_file = create_test_data()

    print("正在生成改进的HTML文件...")

    # 创建可视化器
    visualizer = ITraceTimelineVisualizer(test_file)

    # 生成可视化
    success = visualizer.create_visualization('itrace_timeline_with_worst5.html')

    if success:
        print("✅ HTML文件生成成功: itrace_timeline_with_worst5.html")
        print("🎯 现在包含了完整的 Worst 5% 功能和垂直滚动跳转")
    else:
        print("❌ HTML文件生成失败")

if __name__ == "__main__":
    main()
